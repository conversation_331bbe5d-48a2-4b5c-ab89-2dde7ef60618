import { Meta, StoryObj } from "@storybook/angular";
import { PackageDetailsComponent } from "./package-details.component";

const meta: Meta<PackageDetailsComponent> = {
  title: "BusinessPackages/Shared/PackageDetails",
  component: PackageDetailsComponent,
  tags: ["autodocs"],
  render: (args: PackageDetailsComponent) => ({
    props: args,
  }),
};

export default meta;
type Story = StoryObj<PackageDetailsComponent>;

export const Default: Story = {
  args: {
    planTitle: "خطة المبتدئين (Starter Plan) ",
    price: '3,840',
    currency: "جنيه",
    billingCycle: "شهريًا",
    duration: "30 يومًا",
    adCount: 40,
    availablePoints: 3400,
    features: [
      "شعار",
      "صورة الغلاف",
      "رابط خارجي (الموقع الإلكتروني)",
      "تحليلات أساسية"
    ],
    support: "دعم عبر البريد الإلكتروني",
    renewalNote: "سيتم تجديد تلقائيًا بعد 30 يوم"
  },
};

export const ExtendedPackage: Story = {
  args: {
    planTitle: "برو للأعمال (Business Pro)",
    price: '7,000',
    currency: "جنيه",
    billingCycle: "شهريًا",
    duration: "60 يومًا",
    adCount: 100,
    availablePoints: 7500,
    features: [
      "ملف شخصي كامل الهوية",
      "روابط خارجية (موقع إلكتروني وروابط التواصل الاجتماعي)",
      "شعار تعريفي (Slogan)",
      " قائمة أفضل البائعين",
      "تحليلات مركز التاجر"
    ],
    support: " واتساب + بريد إلكتروني",
    renewalNote: "سيتم تجديد تلقائيًا بعد 60 يوم"
  },
};
