.plans-modal {
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 2px 20px rgba(0,0,0,0.25);
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.plans-header {
  background: radial-gradient(ellipse 66% 66% at 50% 38%, rgba(255,205,132,0.2) 0%, #FAAF40 87%);
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}
.header-content {
  padding: 0 24px 38px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.support-btn {
  direction: ltr;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 4px;
}
.text-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: transparent;
  border: none;
  color: #722282;
  font-weight: 700;
  font-size: 14px;
  cursor: pointer;
}
.hero-image {
  display: flex;
  justify-content: center;
}

.plans-body {
  padding: 24px;
}

