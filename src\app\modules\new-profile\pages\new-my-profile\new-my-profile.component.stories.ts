import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { NewMyProfileComponent } from './new-my-profile.component';
import { NewProfileHeaderComponent } from '../../components/new-profile-header/new-profile-header.component';
import { CommonModule } from '@angular/common';

const meta: Meta<NewMyProfileComponent> = {
  title: 'Profile/MyProfile',
  component: NewMyProfileComponent,
  decorators: [
    moduleMetadata({
      imports: [CommonModule,NewProfileHeaderComponent],
    }),
  ],
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<NewMyProfileComponent>;

export const Default: Story = {
  args: {},
};
