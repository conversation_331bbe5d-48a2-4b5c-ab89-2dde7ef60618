import { HttpClient, provideHttpClient, withFetch } from '@angular/common/http';
import { importProvidersFrom, NgModule } from '@angular/core';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { Observable, of, tap } from 'rxjs';
import { NtranslatePipe } from '../src/app/shared/pipes/ntranslate.pipe';



class CachedTranslateHttpLoader extends TranslateHttpLoader {
    private cacheKey = 'sb_translate_cache_';
    private cacheTimeKey = 'sb_ttranslate_cache_time_';
    private cacheExpiryMinutes = 10;

    constructor(http: HttpClient, prefix?: string, suffix?: string) {
        super(http, prefix, suffix);
    }

    override getTranslation(lang: string): Observable<any> {
        const cacheKey = this.cacheKey + lang;
        const timeKey = this.cacheTimeKey + lang;

        const cachedData = localStorage.getItem(cacheKey);
        const cachedTime = localStorage.getItem(timeKey);

        if (cachedData && cachedTime) {
            const now = new Date().getTime();
            const cacheTime = parseInt(cachedTime, 10);
            const tenMinutesInMs = this.cacheExpiryMinutes * 60 * 1000;

            if (now - cacheTime < tenMinutesInMs) {
                const parsedData = JSON.parse(cachedData);
                return of(this.transformKeys(parsedData));
            }
        }

        return super.getTranslation(lang).pipe(
            tap((translations: any) => {
                const transformedTranslations = this.transformKeys(translations);
                localStorage.setItem(cacheKey, JSON.stringify(transformedTranslations));
                localStorage.setItem(timeKey, new Date().getTime().toString());
            })
        );
    }

    private transformKeys(obj: any): any {
        if (typeof obj !== 'object' || obj === null) {
            return obj;
        }

        if (Array.isArray(obj)) {
            return obj.map(item => this.transformKeys(item));
        }

        const result: any = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                const transformedKey = key.trim().toLowerCase();
                result[transformedKey] = this.transformKeys(obj[key]);
            }
        }
        return result;
    }
}

export function createTranslateLoader(http: HttpClient) {
    return new CachedTranslateHttpLoader(http, "https://adminapidev.4sw.app/api/language/i18n?lang=", "");
}


@NgModule({
    exports: [TranslateModule],
    providers: [
        provideHttpClient(withFetch()),
        importProvidersFrom([
            TranslateModule.forRoot({
                loader: {
                    provide: TranslateLoader,
                    useFactory: createTranslateLoader,
                    deps: [HttpClient],
                },
            }),
            NtranslatePipe
        ])
    ],
})
export class SBTranslateModule { }