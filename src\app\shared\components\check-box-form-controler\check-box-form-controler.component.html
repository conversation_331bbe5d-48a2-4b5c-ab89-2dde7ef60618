<div
  class="section-card"
  (click)="toggleCheck()"
  [formGroup]="form"
  [class.selected]="isChecked"
>
  <input
    [type]="type"
    [checked]="isChecked"
    [disabled]="isDisabled"
    [attr.name]="type === 'radio' ? groupName : null"
  />

  <span class="check-circle">
    <ng-container *ngIf="isChecked; else notChecked">
      <app-svg-icons  width="19px" height="18px" name="icon-circle-check"></app-svg-icons>
    </ng-container>
    <ng-template #notChecked>
      <app-svg-icons  width="19px" height="18px" name="icon-circle-outline"></app-svg-icons>
    </ng-template>
  </span>
      <app-svg-icons [width]="svgIconWidth" [height]="svgIconHeight" [name]="svgIconName"></app-svg-icons>
  <span class="label">{{ label }}</span>
</div>
