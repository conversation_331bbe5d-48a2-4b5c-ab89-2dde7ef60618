import { Meta, StoryObj } from '@storybook/angular';
import { NewRangeInputComponent } from './new-range-input.component';


const meta: Meta<NewRangeInputComponent> = {
  title: 'Shared/NewRangeInput',
  component: NewRangeInputComponent,
  tags: ['autodocs'],
  render: (args) => ({
    props: args,
  }),
};

export default meta;
type Story = StoryObj<NewRangeInputComponent>;

export const Default: Story = {
  args: {
    header: 'حدد عدد الإعلانات',
    min: 0,
    max: 10,
    step: 1,
    value: 5,
  },
};

export const WideRange: Story = {
  args: {
    header: 'نطاق واسع',
    min: 0,
    max: 100,
    step: 5,
    value: 50,
  },
};


export const StepTwo: Story = {
  args: {
    header: 'الزيادات = 2',
    min: 2,
    max: 20,
    step: 2,
    value: 8,
  },
};