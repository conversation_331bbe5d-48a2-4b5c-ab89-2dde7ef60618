import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'sb-locale-manager',
  template: '<ng-content></ng-content>',
})
export class SBLocaleManagerComponent implements OnInit, OnChanges {
  @Input() locale: string = 'ar-eg';

  constructor(private translateService: TranslateService) { }

  ngOnInit() {
    this.setLanguage(this.locale);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['locale'] && !changes['locale'].firstChange) {
      this.setLanguage(changes['locale'].currentValue);
    }
  }

  private setLanguage(locale: string) {
    const lang = locale;

    this.translateService.reloadLang(lang).subscribe(() => {
      this.translateService.use(lang);
    });

  }
}
