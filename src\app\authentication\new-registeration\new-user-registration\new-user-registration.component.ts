import { CommonModule } from "@angular/common";
import { Component } from "@angular/core";
import { FormBuilder, FormGroup, ReactiveFormsModule } from "@angular/forms";
import { CheckBoxFormControlerComponent } from "@src/app/shared/components/check-box-form-controler/check-box-form-controler.component";
import { TermsToggleComponent } from "../shared/terms-toggle/terms-toggle.component";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";

@Component({
  selector: "app-new-user-registration",
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CheckBoxFormControlerComponent,
    TermsToggleComponent,
    SharedBtnComponent,
  ],
  templateUrl: "./new-user-registration.component.html",
  styleUrl: "./new-user-registration.component.scss",
})
export class NewUserRegistrationComponent {
  form: FormGroup;
  agreeTerms = false;
  title: string = "إنشاء حساب جديد";

  constructor(private fb: FormBuilder) {
    this.form = this.fb.group({
      accountType: this.fb.control([]),
    });
  }

  onTermsChange(value: boolean) {
    this.agreeTerms = value;
  }

  next() {
    if (this.form.valid && this.agreeTerms) {
      const selectedAccount = this.form.value.accountType[0];
      console.log("Account type:", selectedAccount);
    }
  }
}
