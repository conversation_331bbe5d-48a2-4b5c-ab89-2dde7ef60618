<div class="ratings-wrapper">
  <div class="ratings-header">
    <div class="ratings-title">التقييمات</div>
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <path d="M7.293 15.7073C7.65912 16.0734 8.23813 16.096 8.6309 15.7756L8.70707 15.7073L15.7071 8.70712C16.0976 8.31659 16.0976 7.68357 15.7071 7.29304L8.70707 0.2929C8.31655 -0.0976324 7.68353 -0.0976324 7.293 0.2929C6.90248 0.683431 6.90248 1.31646 7.293 1.70699L12.586 7.00006H1C0.447717 7.00006 0 7.44778 0 8.00008C0 8.55238 0.447717 9.0001 1 9.0001H12.586L7.293 14.2932C6.90248 14.6837 6.90248 15.3167 7.293 15.7073Z"
          fill="#722282" />
      </svg>
  </div>

  <div class="ratings-summary">
    <div class="summary-info">
      <div class="stars">
        <ng-container *ngFor="let s of [1,2,3,4,5]; let i = index">
          <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
          xmlns="http://www.w3.org/2000/svg">
          <path d="M7 1.17L8.8 4.82L12.83 5.41L9.92 8.25L10.61 12.26L7 10.37L3.39 12.26L4.08 8.25L1.17 5.41L5.2 4.82L7 1.17Z"
          [attr.fill]="i < round(totalRating) ? '#FAAF40' : 'none'"
          stroke="#FAAF40" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </ng-container>
      <span class="rating-count">{{ ratingCount }} التقييمات</span>
      </div>
      <div class="total-rating">{{ totalRating }}</div>
    </div>

    <div class="distribution" *ngFor="let dist of ratingDistribution">
      <div class="row">
        <div class="count">{{ dist.count }}</div>
        <div class="bar"><div class="fill" [style.width.%]="(dist.count / ratingDistribution[0].count) * 100"></div></div>
        <div class="star-display">
          <div data-svg-wrapper class="star-icon">
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6.99996 1.16666L8.80246 4.81833L12.8333 5.4075L9.91663 8.24833L10.605 12.2617L6.99996 10.3658L3.39496 12.2617L4.08329 8.24833L1.16663 5.4075L5.19746 4.81833L6.99996 1.16666Z"
                fill="var(--Secondary, #FAAF40)" stroke="var(--Secondary, #FAAF40)" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
          </div>
          <div class="star-number">{{ dist.stars }}</div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="review" *ngFor="let review of reviews">
    <div class="stars">
      <ng-container *ngFor="let s of [1,2,3,4,5]; let i = index">
        <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
          xmlns="http://www.w3.org/2000/svg">
          <path d="M7 1.17L8.8 4.82L12.83 5.41L9.92 8.25L10.61 12.26L7 10.37L3.39 12.26L4.08 8.25L1.17 5.41L5.2 4.82L7 1.17Z"
            [attr.fill]="i < review.stars ? '#FAAF40' : 'none'"
            stroke="#FAAF40" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </ng-container>
    </div>
    <div class="meta">
      <div class="time">{{ review.time }}</div>
      <div class="user">{{ review.user }}</div>
    </div>
    <div class="ad">
      <span class="label">الاعلان:</span>
      <span>{{ review.ad }}</span>
    </div>
    <div class="comment" *ngIf="review.comment">
      <div class="label">تعليق:</div>
      <div class="text">{{ review.comment }}</div>
    </div>
    <hr />
  </div>
</div>