import { Meta, StoryObj } from '@storybook/angular';
import { InputValueComponent } from './input-value.component';

const meta: Meta<InputValueComponent> = {
  title: 'Shared/InputValue',
  component: InputValueComponent,
  tags: ['autodocs'],
  render: (args) => ({
    props: args,
  }),
};

export default meta;
type Story = StoryObj<InputValueComponent>;

export const Default: Story = {
  args: {
    header: 'حدد النقاط',
    type: 'number',
    value: '342',
  },
};

export const TextType: Story = {
  args: {
    header: 'أدخل الاسم',
    type: 'text',
    value: 'الاسم ثلاثي',
  },
};

export const PasswordType: Story = {
  args: {
    header: 'كلمة المرور',
    type: 'password',
    value: 'password',
  },
};
