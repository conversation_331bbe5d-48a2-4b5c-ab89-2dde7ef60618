import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';

type inputType = 'number' | 'text' | 'password';

@Component({
  selector: 'app-input-value',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './input-value.component.html',
  styleUrl: './input-value.component.scss'
})
export class InputValueComponent {
  @Input() header: string = 'حدد النقاط';
  @Input() type: inputType = 'number';
  @Input() value: string = '';
  @Output() valueChange = new EventEmitter<string>();
  showPassword = false;


  onValueChange(event: Event) {
    const input = event.target as HTMLInputElement;
    this.value = input.value;
    this.valueChange.emit(this.value);    
  }
  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }
}
