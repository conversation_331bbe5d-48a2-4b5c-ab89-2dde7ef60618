import { Component, EventEmitter, Input, Output } from "@angular/core";

@Component({
  selector: "app-terms-toggle",
  standalone: true,
  imports: [],
  templateUrl: "./terms-toggle.component.html",
  styleUrl: "./terms-toggle.component.scss",
})
export class TermsToggleComponent {
  @Input() checked = false;
  @Input() disabled = false;
  @Output() checkedChange = new EventEmitter<boolean>();
  @Input() label: string = 'اوافق على الشروط والاحكام';
  toggle() {
    if(this.disabled) return;
    this.checked = !this.checked;
    this.checkedChange.emit(this.checked);
  }
}
