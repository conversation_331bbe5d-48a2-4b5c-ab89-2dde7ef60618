<button
  class="shared-btn"
  [ngClass]="'shared-btn--' + size"
  [disabled]="disabled"
  [style.background]="
    !disabled
      ? 'linear-gradient(180deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0) 100%), ' +
        bgcolor
      : null
  "
  [style.box-shadow]="!disabled ? '0px 0px 0px 1px' + boxShadow : null"
  (click)="onClick()"
>
  <div class="label" [style.color]="!disabled ? (labelColor) : '#72228233'">
    {{ label }}
    <app-svg-icons
      *ngIf="iconName"
      [name]="iconName"
      [width]="iconWidth"
      [height]="iconHeight"
    ></app-svg-icons>
  </div>
</button>
