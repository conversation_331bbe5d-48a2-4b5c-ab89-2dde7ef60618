<div class="ad-card" [ngClass]="{ selected: isSelected && isSelectedMode }"
    [style.cursor]="isSelectedMode ? 'pointer' : 'default'" *ngIf="listingDetails">
    <div class="ad-card-content">
        <div class="ad-card-right">
            <label class="custom-checkbox" *ngIf="isSelectedMode">
                <input type="checkbox" [checked]="isSelected" />
                <span class="checkmark"></span>
            </label>

            <img [src]="listingDetails.imageUrl" alt="Ad Image" />
        </div>

        <div class="ad-card-left">
            <div>
                <div class="ad-header">
                    <h3 class="ad-title">{{ listingDetails.title }}</h3>
                </div>

                <div class="metrics">
                    <div class="metric">
                        <div>
                            <app-svg-icons name="eye-icon" width="13px" height="13px"></app-svg-icons>
                            <span class="value">{{ listingDetails.appearanceCount }}</span>
                        </div>
                        <span class="label">{{'Call' | translate }}</span>
                    </div>
                    <div class="metric">
                        <div>
                            <app-svg-icons name="arrow-top-left" width="13px" height="13px"></app-svg-icons>
                            <span class="value">{{ listingDetails.viewCount }}</span>
                        </div>
                        <span class="label">{{'views' | translate }}</span>
                    </div>
                    <div class="metric">
                        <div>
                            <app-svg-icons name="phone-icon" width="13px" height="13px"></app-svg-icons>
                            <span class="value">{{ listingDetails.callCount }}</span>
                        </div>
                        <span class="label">{{'Call' | translate }}</span>
                    </div>
                </div>

                <div class="tags">
                    <div class="tag-label" *ngFor="let tag of listingDetails.tags">
                        <app-svg-icons name="featured-icon" width="13px" height="13px"></app-svg-icons>
                        <span>{{ tag }}</span>
                    </div>
                </div>
            </div>
            <app-svg-icons name="back-icon" width="13px" height="13px"></app-svg-icons>
        </div>

    </div>

    <div class="action-button" *ngIf="!isSelectedMode">
        <button>{{'AddItems' | translate}}</button>
    </div>
</div>