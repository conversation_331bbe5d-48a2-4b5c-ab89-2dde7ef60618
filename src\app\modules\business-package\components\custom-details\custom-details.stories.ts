import { Meta, StoryObj } from '@storybook/angular';
import { CustomDetailsComponent } from './custom-details.component';

const meta: Meta<CustomDetailsComponent> = {
  title: 'BusinessPackages/Shared/CustomDetails',
  component: CustomDetailsComponent,
  tags: ['autodocs'],
  render: (args: CustomDetailsComponent) => ({
    props: args,
  }),
};

export default meta;
type Story = StoryObj<CustomDetailsComponent>;

export const Default: Story = {
  args: {
    duration: '90 يومًا',
    adCount: 'حسب الطلب',
    availablePoints: 'حسب الطلب',
    features: [
      ' جميع ميزات Power Seller',
      'تكامل API',
      'تحليلات تفصيلية من مركز التاجر',
      ' تصدير بيانات التفاعل المرتبطة بعملاء محددين',
    ],
    support: 'اتفاقية مستوى خدمة خاصة (SLA)',
    price: 'حسب الطلب',
    renewalNote: 'سيتم تجديد تلقائياً بعد 90 يوم',
  },
};

