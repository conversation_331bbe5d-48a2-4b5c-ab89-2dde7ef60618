<div>
    <div class="row">
        <div class="col-lg-7">
            <app-item-carousel [items]="items" [viewMode]="true"></app-item-carousel>
            <div class="row">
                <h1 class="item_name">
                    <span class="item_title">{{ details.form.name }} </span>
                    <div class="icon_text">
                        <span>{{ locationName | translate }}</span>
                    </div>
                </h1>
                <div class="d-lg-none">
                    <ng-container [ngTemplateOutlet]="priceBox"></ng-container>
                </div>

                <ul class="itemProps">
                    <li [hidden]="subCategory?.queryParams!['hideListingCondition']">
                        <span>{{ 'Condition' | translate }}</span>
                        <span>{{ details.form.condition.name | translate }}</span>
                    </li>
                    <ng-container *ngFor="let item of props">


                        <ng-container *ngIf="item.propTypeName != 'Boolean'">


                            <ng-container [ngSwitch]="item.propTypeName == 'DropDown'">
                                <ng-container *ngSwitchCase="true">
                                    <li *ngIf="item.value">
                                        <span>{{ item.name | translate }}</span>
                                        <span>{{ item.value | translate}}</span>
                                    </li>
                                </ng-container>
                                <ng-container *ngSwitchDefault>
                                    <li *ngIf="item.value">
                                        <span>{{ item.name | translate }}</span>
                                        <span>{{ item.value}}</span>
                                    </li>
                                </ng-container>
                            </ng-container>


                        </ng-container>
                    </ng-container>
                </ul>
                <div class="description">
                    <h2>{{ 'Description' | translate }}</h2>
                    <div [innerHTML]="details.form.description"></div>
                </div>

                <div class="itemTags" *ngIf="details.form?.keywords && details.form?.keywords.length > 0">
                    <h3>{{ 'Tags' | translate }}</h3>
                    <ul>
                        <ng-container *ngFor="let item of details.form?.keywords">
                            <li>
                                {{ item.name | translate}}
                            </li>
                        </ng-container>
                    </ul>
                </div>
                <div class="itemFeatures" *ngIf="hasFeatures()">
                    <h2>{{ 'Features' | translate }}</h2>
                    <ul>
                        <ng-container *ngFor="let item of props">
                            <ng-container *ngIf="item.propTypeName == 'Boolean' && item.value != ''">
                                <li>
                                    {{ item.name | translate }}
                                </li>

                            </ng-container>
                        </ng-container>
                    </ul>
                </div>
                <div class="d-lg-none bottom-sticky">
                    <ng-container [ngTemplateOutlet]="actionBox"></ng-container>
                </div>

            </div>
        </div>
        <div class="col-lg-5 side-col">
            <div class="d-none d-lg-block">
                <ng-container [ngTemplateOutlet]="priceBox"></ng-container>
                <ng-container [ngTemplateOutlet]="actionBox"></ng-container>
            </div>
        </div>
    </div>

</div>
<ng-template #priceBox>
    <div class="item_price" *ngIf="details.form.price! > 0" [innerHTML]="details.form.price | ncurrency">
    </div>
    <div [ngClass]="{ 'listing_specs' : !(details?.treadBy && (details?.treadBy.length > 0)) && !(intersetedCategories && (intersetedCategories.length > 0))}">

        <app-swappable-items [intersetedCategories]="intersetedCategories" [tradeBy]="details.treadBy"
            [isCash]="details.form.paymentMethod == ListingPaymentMethod.cashandswapp"
            *ngIf="details.form.paymentMethod == ListingPaymentMethod.swapp || details.form.paymentMethod == ListingPaymentMethod.cashandswapp">

        </app-swappable-items>
        <div class="cash_icon" *ngIf="details.form.paymentMethod == ListingPaymentMethod.cash">
            <svg data-id="cash_icon" viewBox="0 0 15 12" fill="nones">
                <g opacity="0.5">
                    <path
                        d="M14.25 0H0.75C0.551088 0 0.360322 0.0790178 0.21967 0.21967C0.0790176 0.360322 0 0.551088 0 0.75V11.25C0 11.4489 0.0790176 11.6397 0.21967 11.7803C0.360322 11.921 0.551088 12 0.75 12H14.25C14.4489 12 14.6397 11.921 14.7803 11.7803C14.921 11.6397 15 11.4489 15 11.25V0.75C15 0.551088 14.921 0.360322 14.7803 0.21967C14.6397 0.0790178 14.4489 0 14.25 0ZM13.5 8.25C12.9033 8.25 12.331 8.48705 11.909 8.90901C11.4871 9.33097 11.25 9.90326 11.25 10.5H3.75C3.75 9.90326 3.51295 9.33097 3.09099 8.90901C2.66903 8.48705 2.09674 8.25 1.5 8.25V3.75C2.09674 3.75 2.66903 3.51295 3.09099 3.09099C3.51295 2.66903 3.75 2.09674 3.75 1.5H11.25C11.25 2.09674 11.4871 2.66903 11.909 3.09099C12.331 3.51295 12.9033 3.75 13.5 3.75V8.25Z"
                        fill="black" />
                    <path
                        d="M7.5 3C5.8455 3 4.5 4.3455 4.5 6C4.5 7.6545 5.8455 9 7.5 9C9.1545 9 10.5 7.6545 10.5 6C10.5 4.3455 9.1545 3 7.5 3ZM7.5 7.5C6.67275 7.5 6 6.82725 6 6C6 5.17275 6.67275 4.5 7.5 4.5C8.32725 4.5 9 5.17275 9 6C9 6.82725 8.32725 7.5 7.5 7.5Z"
                        fill="black" />
                </g>
            </svg>

            <span>{{ 'Cash' | translate}}</span>
        </div>
        <div class="adoption_icon" *ngIf="forAdoption$ | async">
            <svg width="13" height="13" viewBox="0 0 13 13" fill="none">
                <g clip-path="url(#clip0_1466_26607)">
                    <path
                        d="M6.50001 5.68768C4.48374 5.68768 1.62501 8.80463 1.62501 10.7722C1.62501 11.6583 2.30573 12.1877 3.44654 12.1877C4.68661 12.1877 5.50546 11.5509 6.50001 11.5509C7.50319 11.5509 8.32433 12.1877 9.55349 12.1877C10.6943 12.1877 11.375 11.6583 11.375 10.7722C11.375 8.80463 8.51628 5.68768 6.50001 5.68768ZM2.76048 5.3675C2.49642 4.48772 1.6829 3.91795 0.943527 4.09467C0.204152 4.27139 -0.181023 5.12782 0.0830391 6.0076C0.347102 6.88739 1.16062 7.45715 1.89999 7.28043C2.63937 7.10371 3.02454 6.24729 2.76048 5.3675ZM4.91157 4.83989C5.69716 4.63321 6.09021 3.57188 5.78958 2.46942C5.48896 1.36696 4.60841 0.641038 3.82282 0.847718C3.03724 1.0544 2.64419 2.11573 2.94482 3.21819C3.24544 4.32065 4.12624 5.04682 4.91157 4.83989ZM12.0562 4.09492C11.3169 3.91821 10.5036 4.48797 10.2393 5.36776C9.97523 6.24754 10.3604 7.10397 11.0998 7.28069C11.8392 7.4574 12.6524 6.88764 12.9167 6.00785C13.1808 5.12807 12.7956 4.27164 12.0562 4.09492ZM8.08845 4.83989C8.87403 5.04657 9.75458 4.32065 10.0552 3.21819C10.3558 2.11573 9.96278 1.05465 9.1772 0.847718C8.39161 0.640784 7.51107 1.36696 7.21044 2.46942C6.90982 3.57188 7.30286 4.63321 8.08845 4.83989Z"
                        fill="#00A51E" />
                </g>
                <defs>
                    <clipPath id="clip0_1466_26607">
                        <rect width="13" height="13" fill="white" />
                    </clipPath>
                </defs>
            </svg>

            {{ 'for_adoption' | translate}}

        </div>
    </div>



</ng-template>
<ng-template #actionBox>
    <div [ngSwitch]="isLoading$ | async">
        <app-loader-box *ngSwitchCase="true" [title]="'Saving' | translate "></app-loader-box>

        <div *ngSwitchDefault class="item_actions">
            <app-gray-btn [btnText]="'Edit' | translate " btnType="button" [bigBtn]="true"
                (click)="onEdit.emit(true)"></app-gray-btn>
            <app-secondary-btn [btnText]="editMode ? ('Save Listing' | translate) : ('Post Listing' | translate )"
                btnType="button" [bigBtn]="true" (click)="onSubmit.emit(true)"></app-secondary-btn>
        </div>
    </div>

</ng-template>