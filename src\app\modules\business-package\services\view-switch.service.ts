import { Injectable, signal, computed } from '@angular/core';

export type View = 'business' | 'subscription' | 'plan' | 'custom' | 'cancel'; 

@Injectable({ providedIn: 'root' })
export class ViewSwitchService {
  private readonly _view = signal<View>('business');
  readonly view = computed(() => this._view());

  go(next: View) {
    this._view.set(next);
  }
  is = {
    business: computed(() => this._view() === 'business'),
    subscription: computed(() => this._view() === 'subscription'),
    plan: computed(() => this._view() === 'plan'),
    custom: computed(() => this._view() === 'custom'),
    cancel: computed(() => this._view() === 'cancel'),
  };
}
