import { NgFor, NgIf } from "@angular/common";
import { Component } from "@angular/core";

@Component({
  selector: "app-ratings",
  standalone: true,
  imports: [NgFor,NgIf],
  templateUrl: "./new-ratings.component.html",
  styleUrl: "./new-ratings.component.scss",
})
export class NewRatingsComponent {
[x: string]: any;
  totalRating = 4.8;
  ratingCount = 23;

  ratingDistribution = [
    { stars: 5, count: 161 },
    { stars: 4, count: 31 },
    { stars: 3, count: 1 },
    { stars: 2, count: 0 },
    { stars: 1, count: 0 },
  ];

  reviews = [
    {
      user: "محمد علي",
      ad: "شقة للبيع متشطبة استلام فوري",
      time: "منذ ١٧ ساعة",
      stars: 5,
      comment:
        "التعامل ممتاز والتوصيل كان سريع، والتلفزيون وصل بحالة ممتازة. أنصح بالتعامل مع هذا البائع",
    },
    {
      user: "محمد علي",
      ad: "شقة للبيع متشطبة استلام فوري",
      time: "منذ ١٧ ساعة",
      stars: 4,
    },
  ];
  round(n: number): number {
  return Math.round(n);
}
}
