
.plan-card {
  width: 100%;
  padding: 20px;
  margin: 20px 0px;
  background: #EFEFEF; 
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  direction: rtl;
  text-align: right;
}
.price-section {
  display: flex;
  direction: rtl;
  flex-direction: column;
}

.price {
  color: #722282; 
  font-weight: 800;
  font-size: 24px;
  line-height: 28px;
}
.sales-btn {
  width: 100%;
  height: 38px;
  padding: 6px 12px;
  background: white;
  box-shadow: inset 0px -3px 6px rgba(243, 245, 250, 0.6);
  border-radius: 6px;
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  cursor: pointer;
  transition: #722282 0.2s ease, box-shadow 0.2s ease;
  direction: ltr;
}

.sales-btn:hover {
  background: #f8f8f8;
  box-shadow: inset 0px -3px 8px rgba(243, 245, 250, 0.8);
}

.icon-wrapper svg path {
  fill: #722282;
}

.sales-btn-label {
  text-align: center;
  color: #722282;
  font-size: 14px;
  font-weight: 700;
  word-wrap: break-word;
}
