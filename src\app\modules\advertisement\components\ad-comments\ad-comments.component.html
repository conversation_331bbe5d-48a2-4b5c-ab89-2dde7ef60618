<div class="comments-box" [ngClass]="{
    active : comments && comments.length > 0
}">

    <div class="comment_boxes" #commentsBox>
        <div *ngFor="let comment of displayItems" class="comment_box">
            <div class="comment_body_wrapper">
                <div class="comment_body">
                    <div class="image" [routerLink]="['/seller', comment?.userID]">
                        <img [src]="comment.userURL" (error)="comment.userURL = './assets/img/avatar.png'" alt="..." />
                    </div>
                    <div class="name_date">
                        <a class="name" [routerLink]="['/seller', comment?.userID]">
                            <ng-container *ngIf="comment.businessName; else userNameBlock">
                                <strong>{{ comment.businessName }}</strong>
                            </ng-container>
                            <ng-template #userNameBlock>
                                {{ comment.userName }}
                            </ng-template>
                            <span>{{
                                comment.date | fromNow }}</span></a>
                        <span class="date">{{ comment.dateString }} </span>

                        <div class="comment_text">
                            <app-comment-input-form [loading]="loadingUpdate" (onSubmit)="onUpdateComment($event)"
                                [editMode]="isCurrentUser(comment.userID) && selectedUpdateComment == comment.id"
                                [text]="comment.text" [ListingID]="listingID"></app-comment-input-form>
                        </div>
                    </div>
                </div>
                <div class="reply_form">
                    <div class="reply_actions">
                        <div class="reply_text" VerifiedClick (VerifiedClick)="toggleReply(comment.id)">{{ 'Reply' |
                            translate }}</div>
                        <ng-container [ngSwitch]="isCurrentUser(comment.userID)">
                            <ng-container *ngSwitchCase="true">
                                <span class="reply_action_btn" VerifiedClick
                                    (VerifiedClick)="onDeleteComment(comment.id)">
                                    <svg data-id="card_delete_icon" viewBox="0 0 19 21" fill="none">
                                        <path
                                            d="M0 4.63353C0 5.1958 0.453857 5.64966 1.01613 5.64966H1.55806V17.6535C1.55806 19.5029 3.05517 21 4.89773 21H13.3113C15.1539 21 16.651 19.5029 16.651 17.6535V5.64966H17.1929C17.7552 5.64966 18.209 5.1958 18.209 4.63353C18.209 4.0713 17.7552 3.6174 17.1929 3.6174H13.6365V2.56741C13.6365 1.15162 12.4848 0 11.069 0H7.13998C5.7242 0 4.57258 1.15162 4.57258 2.56741V3.6174H1.01613C0.453857 3.6174 0 4.0713 0 4.63353ZM12.6474 8.53547V16.0819C12.6474 16.6442 12.1935 17.0981 11.6313 17.0981C11.069 17.0981 10.6151 16.6442 10.6151 16.0819V8.53547C10.6151 7.9732 11.069 7.51934 11.6313 7.51934C12.1935 7.51934 12.6474 7.9732 12.6474 8.53547ZM6.60484 2.56741C6.60484 2.26934 6.84192 2.03226 7.13999 2.03226H11.0691C11.3671 2.03226 11.6042 2.26934 11.6042 2.56741V3.6174H6.60484L6.60484 2.56741ZM5.56163 8.53547C5.56163 7.9732 6.01548 7.51934 6.57775 7.51934C7.13999 7.51934 7.59388 7.9732 7.59388 8.53547V16.0819C7.59388 16.6442 7.13999 17.0981 6.57775 17.0981C6.01548 17.0981 5.56163 16.6442 5.56163 16.0819V8.53547Z"
                                            fill="currentColor" />
                                    </svg>

                                </span>
                                <span class="reply_action_btn" VerifiedClick (VerifiedClick)="toggleEdit(comment.id)">
                                    <svg data-id="card_edit_icon" viewBox="0 0 21 21" fill="none">
                                        <g>
                                            <path
                                                d="M13.9562 12.32L11.5237 12.9237C11.2974 12.9792 11.0655 13.0085 10.8325 13.0112C10.458 13.0118 10.0873 12.9378 9.74173 12.7936C9.39621 12.6494 9.08286 12.4378 8.81997 12.1712C8.4759 11.8231 8.22686 11.3925 8.09665 10.9207C7.96644 10.4489 7.9594 9.95154 8.07622 9.47622L8.68872 7.04373C8.84285 6.42841 9.16027 5.86612 9.60746 5.41624L11.5666 3.45711C12.1966 2.82714 11.7504 1.75 10.8595 1.75H3.49999C2.57173 1.75 1.6815 2.11875 1.02512 2.77512C0.368748 3.4315 0 4.32173 0 5.24999L0 17.4999C0 18.4282 0.368748 19.3184 1.02512 19.9748C1.6815 20.6312 2.57173 20.9999 3.49999 20.9999H15.7499C16.6782 20.9999 17.5684 20.6312 18.2248 19.9748C18.8812 19.3184 19.2499 18.4282 19.2499 17.4999V10.1404C19.2499 9.24954 18.1728 8.80337 17.5428 9.43333L15.5837 11.3925C15.1343 11.8422 14.5721 12.1626 13.9562 12.32Z"
                                                fill="currentColor" />
                                            <path
                                                d="M20.376 1.13145L19.8633 0.6187C19.6671 0.422549 19.4343 0.266952 19.178 0.160795C18.9217 0.0546384 18.647 0 18.3696 0C18.0923 0 17.8176 0.0546384 17.5613 0.160795C17.305 0.266952 17.0722 0.422549 16.876 0.6187L10.8385 6.65618C10.6143 6.88043 10.4552 7.1614 10.3783 7.46905L9.77105 9.89891C9.72517 10.0819 9.7275 10.2737 9.77781 10.4556C9.82812 10.6375 9.9247 10.8032 10.0581 10.9366C10.1915 11.07 10.3573 11.1666 10.5391 11.2169C10.721 11.2672 10.9128 11.2695 11.0958 11.2237L13.5257 10.6164C13.8333 10.5395 14.1143 10.3804 14.3385 10.1562L20.376 4.11869C20.5722 3.92255 20.7278 3.68969 20.8339 3.43341C20.9401 3.17714 20.9947 2.90246 20.9947 2.62507C20.9947 2.34767 20.9401 2.073 20.8339 1.81672C20.7278 1.56045 20.5722 1.32759 20.376 1.13145Z"
                                                fill="currentColor" />
                                        </g>
                                    </svg>

                                </span>
                            </ng-container>
                            <ng-container *ngSwitchDefault>
                                <span class="reply_action_btn" VerifiedClick (VerifiedClick)="openReport(comment)">
                                    <svg data-id="comment_report_icon" viewBox="0 0 12 13" fill="none">
                                        <path
                                            d="M8.88845 4.68767L10.9757 1.60039C11.1068 1.46928 11.1961 1.30224 11.2323 1.12039C11.2684 0.938539 11.2499 0.750047 11.1789 0.578748C11.108 0.407448 10.9878 0.261033 10.8337 0.158018C10.6795 0.0550023 10.4983 1.19182e-05 10.3128 1.33488e-07H0.9375C0.814367 -6.55899e-05 0.692429 0.0241387 0.578657 0.0712292C0.464884 0.11832 0.361509 0.187373 0.274441 0.274441C0.187373 0.361509 0.11832 0.464884 0.0712292 0.578657C0.0241387 0.692429 -6.55899e-05 0.814367 1.33488e-07 0.9375V11.563C1.33488e-07 11.8116 0.0987722 12.0501 0.274588 12.2259C0.450403 12.4017 0.68886 12.5005 0.9375 12.5005C1.18614 12.5005 1.4246 12.4017 1.60041 12.2259C1.77623 12.0501 1.875 11.8116 1.875 11.563V9.37527H10.3128C10.4983 9.37525 10.6795 9.32026 10.8337 9.21725C10.9878 9.11423 11.108 8.96782 11.1789 8.79652C11.2499 8.62522 11.2684 8.43673 11.2323 8.25488C11.1961 8.07303 11.1068 7.90599 10.9757 7.77488L8.88845 4.68767ZM1.875 7.50027V1.87506H8.04959L6.89986 4.0248C6.81278 4.11183 6.7437 4.21517 6.69657 4.3289C6.64944 4.44264 6.62518 4.56455 6.62518 4.68766C6.62518 4.81078 6.64944 4.93269 6.69657 5.04643C6.7437 5.16016 6.81278 5.2635 6.89986 5.35053L8.04959 7.50027H1.875Z"
                                            fill="currentColor" />
                                    </svg>

                                </span>
                            </ng-container>
                        </ng-container>

                    </div>
                    <form class="add-comment" *ngIf="selectedComment == comment.id">
                        <input [(ngModel)]="replytext" type="text" name="replycomment"
                            (keyup.enter)="replyComment(comment)" [placeholder]="('Reply to' | translate) + ' ' + (comment.businessName ? comment.businessName:
                            comment.userName)" />

                        <div [ngSwitch]="loading">
                            <div *ngSwitchCase="true">
                                <app-loader-icon></app-loader-icon>
                            </div>
                            <div *ngSwitchDefault>
                                <span VerifiedClick (VerifiedClick)="replyComment(comment)">
                                    <svg data-id="send_icon" viewBox="0 0 19 19" fill="none">
                                        <path
                                            d="M18.2279 0.772511C17.88 0.421995 17.4399 0.177213 16.9585 0.0665134C16.4772 -0.044186 15.9744 -0.0162743 15.5083 0.147016L2.17944 4.75702C1.48483 4.99719 0.914634 5.43435 0.52759 6.02356C0.173136 6.57316 -0.0103171 7.21557 0.00044801 7.86947C0.0112131 8.52338 0.215712 9.1594 0.588066 9.69704C0.995844 10.2724 1.57987 10.6923 2.28138 10.9083L5.23951 11.8223C5.4328 11.8825 5.63888 11.8886 5.83537 11.8398C6.03186 11.7911 6.21123 11.6894 6.35399 11.5459L8.72809 9.17176C8.87404 9.02581 9.072 8.94381 9.27841 8.94381C9.48483 8.94381 9.68279 9.02581 9.82874 9.17176C9.9747 9.31772 10.0567 9.51568 10.0567 9.7221C10.0567 9.92851 9.9747 10.1265 9.82874 10.2724L7.45464 12.6465C7.31111 12.7893 7.20945 12.9687 7.16071 13.1652C7.11197 13.3616 7.11801 13.5677 7.17818 13.761L8.09223 16.7192C8.30821 17.4207 8.72809 18.0047 9.30347 18.4125C9.83976 18.788 10.4769 18.9927 11.1316 19H11.1869C11.8223 19.0021 12.4446 18.8184 12.9769 18.4712C13.5661 18.0842 14.0033 17.514 14.2435 16.8194L18.8534 3.49048C19.0164 3.02459 19.0441 2.5221 18.9334 2.04112C18.8227 1.56013 18.5781 1.12031 18.2279 0.772511Z"
                                            fill="#722282" />
                                    </svg>

                                </span>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div *ngFor="let reply of comment.replies" class="comment_body_wrapper replies_boxes">
                <div class="comment_body">
                    <div class="image" [routerLink]="['/seller', reply?.userID]">
                        <img [src]="reply.userURL" (error)="reply.userURL = './assets/img/avatar.png'" alt="..." />
                    </div>
                    <div class="name_date">
                        <a class="name" [routerLink]="['/seller', reply?.userID]">
                            <ng-container *ngIf="reply.businessName; else userNameBlock">
                                <strong>{{ reply.businessName }}</strong>
                            </ng-container>
                            <ng-template #userNameBlock>
                                {{ reply.userName }}
                            </ng-template>

                            <span>{{
                                reply.date | fromNow }}</span></a>
                        <span class="date">{{ reply.dateString }} </span>

                        <div class="comment_text">
                            <app-comment-input-form [loading]="loadingUpdate" (onSubmit)="onUpdateComment($event)"
                                [editMode]="isCurrentUser(reply.userID) && selectedUpdateComment == reply.id"
                                [text]="reply.text" [ListingID]="listingID"></app-comment-input-form>
                        </div>
                    </div>
                </div>
                <div class="reply_form">
                    <div class="reply_actions">
                        <ng-container [ngSwitch]="isCurrentUser(reply.userID)">
                            <ng-container *ngSwitchCase="true">
                                <span class="reply_action_btn" (VerifiedClick)="onDeleteComment(reply.id)">
                                    <svg data-id="card_delete_icon" viewBox="0 0 19 21" fill="none">
                                        <path
                                            d="M0 4.63353C0 5.1958 0.453857 5.64966 1.01613 5.64966H1.55806V17.6535C1.55806 19.5029 3.05517 21 4.89773 21H13.3113C15.1539 21 16.651 19.5029 16.651 17.6535V5.64966H17.1929C17.7552 5.64966 18.209 5.1958 18.209 4.63353C18.209 4.0713 17.7552 3.6174 17.1929 3.6174H13.6365V2.56741C13.6365 1.15162 12.4848 0 11.069 0H7.13998C5.7242 0 4.57258 1.15162 4.57258 2.56741V3.6174H1.01613C0.453857 3.6174 0 4.0713 0 4.63353ZM12.6474 8.53547V16.0819C12.6474 16.6442 12.1935 17.0981 11.6313 17.0981C11.069 17.0981 10.6151 16.6442 10.6151 16.0819V8.53547C10.6151 7.9732 11.069 7.51934 11.6313 7.51934C12.1935 7.51934 12.6474 7.9732 12.6474 8.53547ZM6.60484 2.56741C6.60484 2.26934 6.84192 2.03226 7.13999 2.03226H11.0691C11.3671 2.03226 11.6042 2.26934 11.6042 2.56741V3.6174H6.60484L6.60484 2.56741ZM5.56163 8.53547C5.56163 7.9732 6.01548 7.51934 6.57775 7.51934C7.13999 7.51934 7.59388 7.9732 7.59388 8.53547V16.0819C7.59388 16.6442 7.13999 17.0981 6.57775 17.0981C6.01548 17.0981 5.56163 16.6442 5.56163 16.0819V8.53547Z"
                                            fill="currentColor" />
                                    </svg>

                                </span>
                                <span class="reply_action_btn" (VerifiedClick)="toggleEdit(reply.id)">
                                    <svg data-id="card_edit_icon" viewBox="0 0 21 21" fill="none">
                                        <g>
                                            <path
                                                d="M13.9562 12.32L11.5237 12.9237C11.2974 12.9792 11.0655 13.0085 10.8325 13.0112C10.458 13.0118 10.0873 12.9378 9.74173 12.7936C9.39621 12.6494 9.08286 12.4378 8.81997 12.1712C8.4759 11.8231 8.22686 11.3925 8.09665 10.9207C7.96644 10.4489 7.9594 9.95154 8.07622 9.47622L8.68872 7.04373C8.84285 6.42841 9.16027 5.86612 9.60746 5.41624L11.5666 3.45711C12.1966 2.82714 11.7504 1.75 10.8595 1.75H3.49999C2.57173 1.75 1.6815 2.11875 1.02512 2.77512C0.368748 3.4315 0 4.32173 0 5.24999L0 17.4999C0 18.4282 0.368748 19.3184 1.02512 19.9748C1.6815 20.6312 2.57173 20.9999 3.49999 20.9999H15.7499C16.6782 20.9999 17.5684 20.6312 18.2248 19.9748C18.8812 19.3184 19.2499 18.4282 19.2499 17.4999V10.1404C19.2499 9.24954 18.1728 8.80337 17.5428 9.43333L15.5837 11.3925C15.1343 11.8422 14.5721 12.1626 13.9562 12.32Z"
                                                fill="currentColor" />
                                            <path
                                                d="M20.376 1.13145L19.8633 0.6187C19.6671 0.422549 19.4343 0.266952 19.178 0.160795C18.9217 0.0546384 18.647 0 18.3696 0C18.0923 0 17.8176 0.0546384 17.5613 0.160795C17.305 0.266952 17.0722 0.422549 16.876 0.6187L10.8385 6.65618C10.6143 6.88043 10.4552 7.1614 10.3783 7.46905L9.77105 9.89891C9.72517 10.0819 9.7275 10.2737 9.77781 10.4556C9.82812 10.6375 9.9247 10.8032 10.0581 10.9366C10.1915 11.07 10.3573 11.1666 10.5391 11.2169C10.721 11.2672 10.9128 11.2695 11.0958 11.2237L13.5257 10.6164C13.8333 10.5395 14.1143 10.3804 14.3385 10.1562L20.376 4.11869C20.5722 3.92255 20.7278 3.68969 20.8339 3.43341C20.9401 3.17714 20.9947 2.90246 20.9947 2.62507C20.9947 2.34767 20.9401 2.073 20.8339 1.81672C20.7278 1.56045 20.5722 1.32759 20.376 1.13145Z"
                                                fill="currentColor" />
                                        </g>
                                    </svg>

                                </span>
                            </ng-container>
                            <ng-container *ngSwitchDefault>
                                <span class="reply_action_btn" VerifiedClick (VerifiedClick)="openReport(reply)">
                                    <svg data-id="comment_report_icon" viewBox="0 0 12 13" fill="none">
                                        <path
                                            d="M8.88845 4.68767L10.9757 1.60039C11.1068 1.46928 11.1961 1.30224 11.2323 1.12039C11.2684 0.938539 11.2499 0.750047 11.1789 0.578748C11.108 0.407448 10.9878 0.261033 10.8337 0.158018C10.6795 0.0550023 10.4983 1.19182e-05 10.3128 1.33488e-07H0.9375C0.814367 -6.55899e-05 0.692429 0.0241387 0.578657 0.0712292C0.464884 0.11832 0.361509 0.187373 0.274441 0.274441C0.187373 0.361509 0.11832 0.464884 0.0712292 0.578657C0.0241387 0.692429 -6.55899e-05 0.814367 1.33488e-07 0.9375V11.563C1.33488e-07 11.8116 0.0987722 12.0501 0.274588 12.2259C0.450403 12.4017 0.68886 12.5005 0.9375 12.5005C1.18614 12.5005 1.4246 12.4017 1.60041 12.2259C1.77623 12.0501 1.875 11.8116 1.875 11.563V9.37527H10.3128C10.4983 9.37525 10.6795 9.32026 10.8337 9.21725C10.9878 9.11423 11.108 8.96782 11.1789 8.79652C11.2499 8.62522 11.2684 8.43673 11.2323 8.25488C11.1961 8.07303 11.1068 7.90599 10.9757 7.77488L8.88845 4.68767ZM1.875 7.50027V1.87506H8.04959L6.89986 4.0248C6.81278 4.11183 6.7437 4.21517 6.69657 4.3289C6.64944 4.44264 6.62518 4.56455 6.62518 4.68766C6.62518 4.81078 6.64944 4.93269 6.69657 5.04643C6.7437 5.16016 6.81278 5.2635 6.89986 5.35053L8.04959 7.50027H1.875Z"
                                            fill="currentColor" />
                                    </svg>
                                </span>
                            </ng-container>
                        </ng-container>

                    </div>

                </div>
            </div>

        </div>

        <div class="more_row" *ngIf="hasMoreItems()">
            <div (click)="loadMore()">{{ 'More' | translate }}</div>
        </div>

    </div>

    <div class="formBox" VerifiedClick #target>
        <form class="add-comment">
            <input #commentInput type="text" name="comment" [(ngModel)]="comment" (keyup.enter)="postComment()"
                [placeholder]="'Type a public comment..' | translate" />

            <div [ngSwitch]="loading">
                <div *ngSwitchCase="true">
                    <app-loader-icon></app-loader-icon>
                </div>
                <div *ngSwitchDefault>
                    <span VerifiedClick (VerifiedClick)="postComment()">
                        <svg data-id="send_icon" viewBox="0 0 19 19" fill="none">
                            <path
                                d="M18.2279 0.772511C17.88 0.421995 17.4399 0.177213 16.9585 0.0665134C16.4772 -0.044186 15.9744 -0.0162743 15.5083 0.147016L2.17944 4.75702C1.48483 4.99719 0.914634 5.43435 0.52759 6.02356C0.173136 6.57316 -0.0103171 7.21557 0.00044801 7.86947C0.0112131 8.52338 0.215712 9.1594 0.588066 9.69704C0.995844 10.2724 1.57987 10.6923 2.28138 10.9083L5.23951 11.8223C5.4328 11.8825 5.63888 11.8886 5.83537 11.8398C6.03186 11.7911 6.21123 11.6894 6.35399 11.5459L8.72809 9.17176C8.87404 9.02581 9.072 8.94381 9.27841 8.94381C9.48483 8.94381 9.68279 9.02581 9.82874 9.17176C9.9747 9.31772 10.0567 9.51568 10.0567 9.7221C10.0567 9.92851 9.9747 10.1265 9.82874 10.2724L7.45464 12.6465C7.31111 12.7893 7.20945 12.9687 7.16071 13.1652C7.11197 13.3616 7.11801 13.5677 7.17818 13.761L8.09223 16.7192C8.30821 17.4207 8.72809 18.0047 9.30347 18.4125C9.83976 18.788 10.4769 18.9927 11.1316 19H11.1869C11.8223 19.0021 12.4446 18.8184 12.9769 18.4712C13.5661 18.0842 14.0033 17.514 14.2435 16.8194L18.8534 3.49048C19.0164 3.02459 19.0441 2.5221 18.9334 2.04112C18.8227 1.56013 18.5781 1.12031 18.2279 0.772511Z"
                                fill="#722282" />
                        </svg>

                    </span>
                </div>
            </div>





        </form>
    </div>





</div>



<p-dialog header="" [closable]="true" [modal]="true" [(visible)]="reportVisible" [style]="{width: '50vw'}">
    <form [formGroup]="form" class="reason_form">
        <div class="modelTop reasons_box">
            <h3>{{ 'Report Comments' | translate}}</h3>
            <ng-container *ngIf="reasons$ | async as reasons">
                <div *ngFor="let reason of reasons" class="reason_item">
                    <p-radioButton [inputId]="reason.id.toString()" formControlName="report"
                        [value]="reason.id"></p-radioButton>
                    <label [for]="reason.id">{{ reason.name | translate }}</label>
                </div>
            </ng-container>

        </div>
        <div class="model_actions">
            <app-dark-btn [btnDisabled]="!form.valid" [btnText]="'Ok' | translate" btnType="button" [bigBtn]="true"
                (click)="report()"></app-dark-btn>
        </div>
    </form>
</p-dialog>