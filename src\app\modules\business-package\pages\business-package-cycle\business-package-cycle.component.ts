import { CommonModule } from '@angular/common';
import { Component, inject, signal, Signal } from '@angular/core';
import { BusinessPackageComponent } from '../business-package/business-package.component';
import { SubscriptionPackageComponent } from '../../components/subscription-package/subscription-package.component';
import { PackagePlanComponent } from '../../components/package-plan/package-plan.component';
import { CustomPackagePlanComponent } from '../../components/custom-package-plan/custom-package-plan.component';
import { CancelConfirmationComponent } from '../../components/cancel-confirmation/cancel-confirmation.component';
import { ViewSwitchService } from '../../services/view-switch.service';
import { CustomSubscriptionComponent } from "../../components/custom-subscription/custom-subscription.component";

@Component({
  selector: 'app-business-package-cycle',
  standalone: true,
  imports: [CommonModule, BusinessPackageComponent, SubscriptionPackageComponent, PackagePlanComponent, CancelConfirmationComponent, CustomSubscriptionComponent],
  templateUrl: './business-package-cycle.component.html',
  styleUrl: './business-package-cycle.component.scss'
})
export class BusinessPackageCycleComponent {
 
  public viewSvc = inject(ViewSwitchService);
   plan = {
    title: 'خطة المبتدئين (Starter Plan)',
    price: '3,840',
  };

  proPlan = {
    title: 'برو للأعمال (Business Pro)',
    price: '7,000',
  };

  custom = {
    duration: '90 يومًا',
    adCount: 'حسب الطلب',
    availablePoints: 'حسب الطلب',
    features: ['جميع ميزات Power Seller', 'تكامل API', 'تحليلات تفصيلية من مركز التاجر', 'تصدير بيانات التفاعل المرتبطة بعملاء محددين'],
    support: 'اتفاقية مستوى خدمة خاصة (SLA)',
    price: 'حسب العرض (Quotation)',
    renewalNote: '',
  };

  subscription = {
    titleText: ' اشترك في باقة خطة المبتدئين',
    planTitle: 'خطة المبتدئين',
    price: '3,840',
  };

  cancelInfo = {
    titleText: 'هل أنت متأكد من رغبتك في تحويل الي حساب فرد؟  ',
    cancelDate: 'عند تحويل نوع الحساب ، سيتم إزالة أي إضافة او اعلانات تم شراؤها مسبقًا',
    btnText: 'تأكيد التحويل ',
  };
}
