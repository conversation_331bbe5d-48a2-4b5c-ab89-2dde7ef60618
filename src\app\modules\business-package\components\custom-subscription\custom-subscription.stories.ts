import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { CustomSubscriptionComponent } from './custom-subscription.component';
import { CustomDetailsComponent } from '../custom-details/custom-details.component';

const meta: Meta<CustomSubscriptionComponent> = {
  title: 'BusinessPackages/CustomSubscription',
  component: CustomSubscriptionComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [CustomDetailsComponent],
    }),
  ],
  render: (args: CustomSubscriptionComponent) => ({
    props: args,
  }),
};

export default meta;
type Story = StoryObj<CustomSubscriptionComponent>;

export const Default: Story = {
  args: {
    titleText: '(Enterprise) المؤسسات ',
  },
};