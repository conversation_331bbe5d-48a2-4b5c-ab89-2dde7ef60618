import { CommonModule } from '@angular/common';
import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Store } from '@ngrx/store';
import { AppcenterService } from '@src/app/shared/services/appcenter.service';
import { DialogModule } from 'primeng/dialog';
import { RadioButtonModule } from 'primeng/radiobutton';
import { Observable, Subject, Subscription, finalize, map, takeUntil } from 'rxjs';
import { User } from 'src/app/authentication/models/dto/user.dto.model';
import { CommentInputFormComponent } from 'src/app/modules/advertisement/components/comment-input-form/comment-input-form.component';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { DarkBtnComponent } from 'src/app/shared/components/dark-btn/dark-btn.component';
import { LoaderIconComponent } from 'src/app/shared/components/loader-icon/loader-icon.component';
import { VerifiedClickDirective } from 'src/app/shared/directives/isverifiedclick.directive';
import { Comment, DeleteComment, EditComment, PostComment } from 'src/app/shared/models/comments.model';
import { LookupDTO } from "src/app/shared/models/lookup.model";
import { ReportComment } from 'src/app/shared/models/reports.model';
import { FromNowPipe } from 'src/app/shared/pipes/from-now.pipe';
import { NtranslatePipe } from 'src/app/shared/pipes/ntranslate.pipe';
import { AuthService } from 'src/app/shared/services/auth.service';
import { CommentsService } from 'src/app/shared/services/comments.service';
import { ListingService } from 'src/app/shared/services/listing.service';
import { ReportsService } from 'src/app/shared/services/reports.service';
import { SellerService } from 'src/app/shared/services/seller.service';
import { getUser } from 'src/app/store/app/selectors/app.selector';
@UntilDestroy({ checkProperties: true })
@Component({
  selector: 'app-ad-comments',
  standalone: true,
  imports: [CommonModule, FormsModule, NtranslatePipe, DialogModule, ReactiveFormsModule, LoaderIconComponent, VerifiedClickDirective, RadioButtonModule, DarkBtnComponent, CommentInputFormComponent, RouterModule, FromNowPipe],
  templateUrl: './ad-comments.component.html',
  styleUrls: ['./ad-comments.component.scss']
})
export class AdCommentsComponent implements OnInit, OnDestroy {

  @ViewChild('commentInput') commentInput: ElementRef;

  @ViewChild('target') targetHost: ElementRef<HTMLInputElement>;
  @ViewChild('commentsBox') commentsBox: ElementRef;

  @Input() listingID;
  displayItems: Comment[] = [];
  comments: Comment[] = [];
  comment: string = '';
  replytext: string = '';
  private destroy$ = new Subject<void>();
  loading: boolean = false;
  loadingUpdate: boolean = false;
  loadingReply: boolean = false;
  fromNow: FromNowPipe;
  watcher: Subscription;
  selectedComment = 0;
  reportedComment = 0;
  currentUser: User;
  selectedUpdateComment = 0;
  form: FormGroup;


  reasons$: Observable<LookupDTO[]>;
  reportVisible: boolean = false;
  constructor(
    private _commentsService: CommentsService,
    private _sellerService: SellerService,
    private _authService: AuthService,
    private _listingService: ListingService,
    private langService: AppcenterService,
    private store: Store,
    private fb: FormBuilder,
    private reportService: ReportsService,
    private alertService: AlertHandlerService,
    private translateService: TranslationService,
    private browser: BrowserService,

  ) {
    this.store.select(getUser).pipe(untilDestroyed(this)).subscribe(res => {
      if (!res) return;
      this.currentUser = res;
    })
  }

  getComments() {
    this._commentsService.getlistingComments(this.listingID).subscribe((x) => {
      if (!x.succeded) return;
      this.comments = x.data;
      this.displayItems = x.data;
      if (!this.comments) return;
      let user = this.comments.map((x) => x.userID);

      let replies = this.comments.map((x) => x.replies.map((y) => y.userID));
      let arr = replies.reduce((acc, curVal) => {
        return acc.concat(curVal);
      }, []);
      arr.forEach((x) => {
        user.push(x);
      });
      //this.getProfiles(user);
    });
  }

  loadMore() {
    const remainingItems = this.comments.slice(this.displayItems.length);
    this.displayItems.push(...remainingItems.splice(0, 3));
  }

  hasMoreItems(): boolean {
    return this.displayItems.length < this.comments.length;
  }

  ngOnDestroy(): void {
    this.destroy$.complete();
    this.watcher.unsubscribe();
  }

  isCurrentUser(userID) {
    return this.currentUser && this.currentUser.id == userID;
  }

  toggleEdit(id) {
    this.selectedComment = 0;
    this.replytext = '';
    if (this.selectedUpdateComment == id) {
      this.selectedUpdateComment = 0;
    } else {
      this.selectedUpdateComment = id;
    }

  }

  onUpdateComment(e) {
    if (e.startsWith('\n')) return;
    if (e.length == 0) return;
    if (this.loadingUpdate) return;
    this.loadingUpdate = true;
    let model: EditComment = {
      text: e,
      id: this.selectedUpdateComment,
    };
    this._commentsService.editComment(model).subscribe({
      next: (x) => {
        if (x.succeded) {
          this.selectedUpdateComment = 0;
          this.loadingUpdate = false;
          this.getComments();
        }
      },
      error: (err) => {
        this.selectedUpdateComment = 0;
        this.loadingUpdate = false;
        this.getComments();
      }
    });
  }

  onDeleteComment(id) {
    let model: DeleteComment = {
      id: id,
      userID: this.currentUser.id.toString(),
    };
    this.alertService.warn({
      message: this.translateService.instant("delete_comment_confirm"),
      buttons: [
        { title: this.translateService.instant("Yes, Remove"), value: true },
        { title: this.translateService.instant("No, Keep"), value: false },
      ]
    }, (e) => {
      if (e) {
        this._commentsService.deleteComment(model).subscribe({
          next: (x) => {
            if (x.succeded) {
              this.getComments();
            }
          },
          error: (err) => {
            this.getComments();
          }
        });
      }
    });
  }



  toggleReply(id) {


    this.selectedUpdateComment = 0;
    this.replytext = '';
    if (this.selectedComment == id) {
      this.selectedComment = 0;
    } else {
      this.selectedComment = id;
    }

  }

  replyComment(comment) {
    if (this.replytext.startsWith('\n')) return;
    if (this.replytext.length == 0) return;
    if (this.loadingReply) return;
    this.loadingReply = true;
    let reply: PostComment = { text: this.replytext, parentID: comment.id, listingID: this.listingID };
    this._commentsService.postComment(reply).subscribe({
      next: (x) => {
        {
          this.replytext = '';
          this.selectedComment = 0;
          this.loadingReply = false;
          this.getComments();
        }
      },
      error: (err) => {
        this.replytext = '';
        this.selectedComment = 0;
        this.loadingReply = false;
        this.getComments();
      }
    });
  }

  ngOnInit(): void {

    this.fromNow = new FromNowPipe(this.langService, this.translateService);

    this.form = this.fb.group({
      report: new FormControl('', [Validators.required]),
    });


    this.getComments();
    this.watcher = this._listingService.scroll
      .pipe(takeUntil(this.destroy$))
      .subscribe(res => {
        if (res) {
          this.scrollToComment();
        }
      });
  }



  report() {
    this.reportVisible = false;
    let model: ReportComment = {
      reason: this.form.value.report,
      commentID: this.reportedComment,
    };

    this.reportService.reportComment(model).subscribe(
      {
        next: x => {
          if (x.succeded) {

            this.alertService.success({
              message: this.translateService.instant("Reported Comment Successfully")
            });
          }
        },
        error: err => {
          if (typeof err === "string" && err == "CommentAlreadyReported") {
            this.alertService.warn({
              message: this.translateService.instant("CommentAlreadyReported")
            });
          }
        }
      }



    );
  }

  isAuthorized() {
    return this._authService.isUserAuthenticated();
  }

  scrollToComment() {
    this.browser.scrollTo({
      behavior: 'smooth',
      top: this.targetHost ? this.targetHost.nativeElement.offsetTop - 150 : 0,
    });
    if (this.commentInput) {
      this.commentInput.nativeElement.focus();

    }
  }

  openReport(comment) {
    this.reportedComment = comment.id;
    this.reasons$ = this.reportService.getCommentReportsReason().pipe(map(res => res.data));
    this.reportVisible = true;
  }





  postComment() {
    if (this.comment.startsWith('\n')) return;
    if (this.comment.length == 0) return;
    let postCommentsModel: PostComment = {
      text: this.comment,
      listingID: this.listingID,
    };
    if (this.loading) return;
    this.loading = true;
    this._commentsService.postComment(postCommentsModel).pipe(finalize(() => {
      this.loading = false;
      this.comment = "";
      this.getComments();
    })).subscribe({

    });
  }

  async getProfiles(ids: string[]) {
    this._sellerService.getUsersProfile(ids).subscribe((x) => {
      this.comments.forEach(async (element) => {
        element.dateString = this.fromNow.transform(element.date.toString()).toString();

        let profile = x.body?.data.filter((x) => x.userID == element.userID)[0];

        if (profile == null) return;
        element.userImage = profile.imageURL!;
        element.userName = profile.firstName + ' ' + profile.lastName;
        element.replies.forEach(async (repl) => {
          repl.dateString = this.fromNow.transform(repl.date.toString()).toString();

          let replprofile = x.body?.data.filter(
            (x) => x.userID == repl.userID
          )[0];
          if (replprofile == null) return;
          repl.userImage = replprofile.imageURL!;
          repl.userName = replprofile.firstName + ' ' + replprofile.lastName;
        });


        // setTimeout(() => {
        //   this.commentsBox.nativeElement.scrollTop = this.commentsBox.nativeElement.scrollHeight;
        // }, 500);

      });
    });
  }

}
