import type { Meta, StoryObj } from '@storybook/angular';
import { fn } from 'storybook/test';

import { NewRatingsComponent } from './new-ratings.component';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<NewRatingsComponent> = {
  title: 'Profile/Rating',
  component: NewRatingsComponent,
  tags: ['autodocs'],
  argTypes: {
  },
};

export default meta;
type Story = StoryObj<NewRatingsComponent>;

export const Primary: Story = {
  args: {
  },
};

export const Secondary: Story = {
  args: {
  },
};

export const Large: Story = {
  args: {
  },
};

export const Small: Story = {
  args: {
    size: 'small',
    label: 'Button',
  },
};
