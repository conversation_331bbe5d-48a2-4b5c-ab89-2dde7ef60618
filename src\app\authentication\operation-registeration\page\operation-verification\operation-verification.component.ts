import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { NgxOtpInputComponent, NgxOtpInputComponentOptions } from 'ngx-otp-input';
import { Button } from 'primeng/button';
import { Subscription, finalize, take, timer } from 'rxjs';
import { RegisterUserWithOTP } from 'src/app/authentication/models/dto/user.dto.model';
import { OperationService } from 'src/app/authentication/operation-registeration/services/operation.service';
import { AlertHandlerService } from 'src/app/modules/core/alerts/alert-handler.service';
import { BrowserService } from 'src/app/modules/core/service/browser.service';
import { TranslationService } from 'src/app/modules/core/service/translation.service';
import { AuthService } from 'src/app/shared/services/auth.service';
import { CommonService, ConversionEvents } from 'src/app/shared/services/common.service';
import { DeviceDetectionService } from 'src/app/shared/services/device-detection.service';
import { ModalService } from 'src/app/shared/services/modal/modal.service';
import { ProfileService } from 'src/app/shared/services/profile.service';
import { FormUtils } from 'src/utils/form-utils';
import { EditPhoneModalComponent } from '../../../../shared/components/edit-phone-modal/edit-phone-modal.component';
import { SecondaryBtnComponent } from '../../../../shared/components/secondary-btn/secondary-btn.component';
import { NtranslatePipe } from '../../../../shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-operation-verification',
  templateUrl: './operation-verification.component.html',
  styleUrls: ['./operation-verification.component.scss'],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [
    NgxOtpInputComponent,
    NgClass,
    NgIf,
    Button,
    SecondaryBtnComponent,
    EditPhoneModalComponent,
    NtranslatePipe,
  ],
})
export class OperationVerificationComponent implements OnInit {

  @ViewChild('ngxotp') ngxOtp: NgxOtpInputComponent;
  otpFilled: boolean = false;

  countDown: Subscription;
  started = false;
  counter = 60;
  tick = 1000;
  phoneNumber: string;
  wrongOTP: boolean = false;
  loading: boolean = false;
  otpInputConfig: NgxOtpInputComponentOptions = {
    otpLength: 4,
    autoFocus: true,

  };
  filledOtp: string = '';
  tryMode = false;
  constructor(
    private _modalService: ModalService,
    private _authService: AuthService,
    private _profileService: ProfileService,
    private _router: Router,
    private alertService: AlertHandlerService,
    private translateService: TranslationService,
    private operationService: OperationService,
    private browser: BrowserService,
    private device: DeviceDetectionService,
    private commonService: CommonService
  ) { }

  ngOnInit(): void {
    this.resetCounter();
    this.sendOTPRegistration();
  }
  resendClicked() {
    if (this.counter > 0) return;
    this.resetCounter();
    this.sendOTPRegistration();
  }

  resetCounter() {
    if (this.countDown) {
      this.countDown.unsubscribe();
    }

    this.counter = 60;
    this.tick = 1000;
    this.countDown = timer(0, this.tick)
      .pipe(take(this.counter))
      .subscribe(() => {
        --this.counter;
        if (this.counter == 0) {
          this.countDown.unsubscribe();
        }
      });

  }
  sendOTPRegistration() {

    this.phoneNumber = this.operationService.getByKey('phone');
    this.wrongOTP = false;


    this._authService
      .generateotpforregistration(this.phoneNumber)
      .subscribe({
        next: (x) => {
          //if (x == true) this.handleFillEvent('Pass');
        },
        error: (x) => {
          this.commonService.pushDataLayer({
            'event': 'website_error',
            'error_type': 'operator registration otp error',
            'error_message': JSON.stringify(x)
          });
          if (x.errors && x.errors.length > 0) {
            this.alertService.warn({ message: this.translateService.instant(x.errors[0] ?? '') }, (e) => {

              if (x.errors[0] == "MaxOTPTrialsReached") {
                this._router.navigate(['/home'], { replaceUrl: true });
              }
              //this.handleFillEvent('Pass');
            });
          } else {
            this.alertService.warn({ message: JSON.stringify(x) }, (e) => {

            });
          }
        }
      });


  }

  tryVerify() {
    this.tryMode = true;
    this.handleFillEvent(this.filledOtp);

  }

  transform(value: number): string {
    const minutes: number = Math.floor(value / 60);
    return (
      ('00' + minutes).slice(-2) +
      ':' +
      ('00' + Math.floor(value - minutes * 60)).slice(-2)
    );
  }

  showDialog() {
    this._modalService.openModal();
  }

  handeOtpChange(value: string[]): void { }

  forceResend() {
    this.resetCounter();
    this.sendOTPRegistration();
  }


  handleFillEvent(value: any): void {

    if (value == '') return;

    this.filledOtp = value;

    let data = this.operationService.getData();

    this.otpFilled = true;


    this.browser.setStorageItem('firstName', data['firstName']);

    value = FormUtils.fixNumbers(value);

    let user = {
      phoneNumber: data['phone'],
      userName: data['phone'],
      firstName: data['firstName'],
      lastName: data['lastName'],
      areaID: FormUtils.safeNumber(data['areaID']) ?? 2,
      email: data['email'],
      otp: value,
      password: data['password'],
      ReferredByCode: data['code'] ?? null,
      AccountType: data['AccountType'],
      defaultLang: 'ar-eg',
      Platform: this.device.isMobile ? 'web mobile' : 'web',
      OSVersion: this.commonService.detectBrowserAndOS()


    } as unknown as RegisterUserWithOTP;

    this.loading = true;


    this._authService
      .registerUserWithOTP('api/account/registration', user).pipe(finalize(() => {
        this.loading = false;
        this.tryMode = false;
      }))
      .subscribe({
        next: (x) => {
          if (!x.isSuccessfulRegistration) {
            if (x.errors && x.errors.length > 0) {
              this.alertService.warn({ message: this.translateService.instant(x.errors[0] ?? '') }, (e) => {
                this.ngxOtp.reset();
              });

            }
            //this.wrongOTP = !x.isSuccessfulRegistration;
            return;
          }

          this.browser.setStorageItem('tempOTP', x.tempToken);
          this.browser.setStorageItem('userID', x.userID);

          this.commonService.pushDataLayer({
            'event': ConversionEvents.Register,
            'authentication_method': this.device.isMobile ? 'mobile' : 'desktop',
            'seller_id': x.userID.toString(),
            'seller_identifier': btoa(user.phoneNumber ?? '')

          });

          this.commonService.pushDataLayer({
            'event': ConversionEvents.RegisterOperator,
            'authentication_method': this.device.isMobile ? 'mobile' : 'desktop',
            'seller_id': x.userID.toString(),
            'seller_identifier': btoa(user.phoneNumber ?? '')

          });




          let image = data['image'];
          if (image) {
            let file = this.dataURLtoFile(image, 'image.jpg');
            let uploadData = new FormData();
            uploadData.append('userprofile', file, file.name);
            uploadData.append('id', x.userID.toString());
            uploadData.append('otp', x.tempToken);
            this._profileService.uploadImgsWithOTP(uploadData).subscribe((x) => {
              this.browser.removeStorageItem('image');
            });
          } else {
            this.operationService.destroy();
          }
          this._router.navigate(['/authentication/register-operator/interests']);
        }, error: (x) => {
          this.commonService.pushDataLayer({
            'event': 'website_error',
            'error_type': 'operator registration error',
            'error_message': JSON.stringify(x)
          });
          if (x.errors && x.errors.length > 0) {
            this.alertService.warn({ message: this.translateService.instant(x.errors[0] ?? '') }, (e) => {

              if (x.errors[0] == "MaxOTPTrialsReached") {
                this._router.navigate(['/home'], { replaceUrl: true });
              }
              //this._router.navigate(['/authentication/register-operator/success']);
            });

          } else {
            this.alertService.warn({ message: JSON.stringify(x) }, (e) => {

            });

          }
          this.loading = false;
        }
      });
  }

  dataURLtoFile(dataurl, filename) {
    var arr = dataurl.split(','),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[arr.length - 1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  }

}
