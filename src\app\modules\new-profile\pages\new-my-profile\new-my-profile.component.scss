.container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.profile-progress-card {
  width: 335px;
  padding: 16px 20px;
  background: rgba(250, 175, 64, 0.10);
  border-radius: 6px;
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  gap: 14px;
  cursor: pointer;
}

.profile-progress-content {
  width: 100%;
  display: inline-flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
  direction: ltr;
}

.profile-progress-bar {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-circle {
  width: 23px;
  height: 23px;
}

.circular-chart {
  width: 100%;
  height: 100%;
  transform: rotate(-180deg);
}

.circle-bg {
  fill: none;
  stroke: white;
  stroke-width: 6;
}

.circle {
  fill: none;
  stroke: #FAAF40;
  stroke-width: 6;
  stroke-linecap: round;
  transition: stroke-dasharray 0.4s ease;
}

.progress-text {
  font-size: 12px;
  font-weight: 700;
  color: #FAAF40;
}

.profile-progress-title {
  flex: 1;
  font-size: 14px;
  font-weight: 600;
  color: #FAAF40;
  text-align: right;
}
.profile-sections {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 16px;
  direction: rtl;

  .icon {
    width: 30px;
    height: 30px;
    fill: currentColor;
  }

  .section-footer {
    grid-column: span 2;
    background-color: #eeeeee;
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;

    .label {
      flex: 1;
      font-size: 14px;
      font-weight: 500;
      color: #272728;
    }
  }
}

app-svg-icons {
  width: 30px !important;
  height: 30px !important;
}

