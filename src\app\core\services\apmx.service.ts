import { Injectable } from '@angular/core';
import { ApmService } from '@elastic/apm-rum-angular';
import { environment } from '@src/environments/environment';

@Injectable({
    providedIn: 'root',
})
export class ApmxService {
    apm: any;
    constructor(private service: ApmService) { }

    context(obj) {
        this.apm.setUserContext(obj);
    }

    init() {
        this.apm = this.service.init({
            serviceName: 'swapp-portal',
            serverUrl: environment.apmServer,
            environment: environment.apmEnvirnment
        });
    }
}