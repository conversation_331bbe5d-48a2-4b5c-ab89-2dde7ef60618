import { Component, inject } from '@angular/core';
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { PackagePlanComponent } from "../../components/package-plan/package-plan.component";
import { CustomPackagePlanComponent } from "../../components/custom-package-plan/custom-package-plan.component";
import { ViewSwitchService } from '../../services/view-switch.service';

@Component({
  selector: 'app-business-package',
  standalone: true,
  imports: [SharedBtnComponent, PackagePlanComponent, CustomPackagePlanComponent],
  templateUrl: './business-package.component.html',
  styleUrl: './business-package.component.scss'
})
export class BusinessPackageComponent {

   private viewSvc = inject(ViewSwitchService);
  goToSubscription() {
    this.viewSvc.go('cancel');
  }
}
