import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-new-range-input',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './new-range-input.component.html',
  styleUrl: './new-range-input.component.scss'
})
export class NewRangeInputComponent {
  @Input() min = 0;
  @Input() max = 10;
  @Input() step = 1;
  @Input() value = 5;
  @Input() header = 'حدد عدد الإعلانات';
  @Output() valueChange = new EventEmitter<number>();

  progressPercent = 0;

  ngOnInit() {
    this.updateProgress();
  }

  onRangeChange(event: Event) {
    const input = event.target as HTMLInputElement;
    this.value = Number(input.value);
    this.updateProgress();
    this.valueChange.emit(this.value);    
  }

  private updateProgress() {
    this.progressPercent = ((this.value - this.min) / (this.max - this.min)) * 100;
  }
}
