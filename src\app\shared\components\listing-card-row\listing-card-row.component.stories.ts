import { Meta, StoryObj } from '@storybook/angular';
import { ListingCardRowComponent } from './listing-card-row.component';

const meta: Meta<ListingCardRowComponent> = {
    title: 'Shared/ListingCardRow',
    component: ListingCardRowComponent,
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<ListingCardRowComponent>;

export const Default: Story = {
    args: {
        isSelected: false,
        isSelectedMode: false,
        listingDetails: {
            id: 1,
            title: 'Redmi note 13 pro 4G',
            imageUrl: '../../../../../../assets/images/image 4.jpg',
            callCount: 200,
            viewCount: 32,
            appearanceCount: 103,
            tags: ['Top Featured'],
        },
    },
};

export const SelectedMode: Story = {
    args: {
        isSelected: true,
        isSelectedMode: true,

        listingDetails: {
            "id": 1,
            "title": "Redmi note 13 pro 4G",
            "imageUrl": "../../../../../../assets/images/image 4.jpg",
            "callCount": 200,
            "viewCount": 32,
            "appearanceCount": 103,
            "tags": ["Top Featured"]
        }
    }
};
