import { NgClass } from '@angular/common';
import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-svg-icons',
  standalone: true,
  imports: [NgClass],
  templateUrl: './svg-icons.component.html',
  styleUrl: './svg-icons.component.scss'
})
export class SvgIconsComponent {
  @Input() name!: string;
  @Input() width?: string = '30px';
  @Input() height?: string = '30px';
  @Input() color?: string = null;
  @Input() customClass?: string;
  
}