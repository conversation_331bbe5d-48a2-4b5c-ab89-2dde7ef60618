import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, Input } from '@angular/core';
import { SvgIconsComponent } from '../svg-icons/svg-icons.component';
import { NtranslatePipe } from '../../pipes/ntranslate.pipe';

@Component({
  selector: 'app-listing-card-row',
  standalone: true,
  imports: [Ng<PERSON>lass, NgIf, NgFor, SvgIconsComponent, NtranslatePipe],
  templateUrl: './listing-card-row.component.html',
  styleUrl: './listing-card-row.component.scss',
})
export class ListingCardRowComponent {

  @Input() isSelectedMode: boolean = false;
  @Input() isSelected: boolean = false;
  @Input() listingDetails: any = {};

}
