import { Component, inject, Input } from '@angular/core';
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { PackageDetailsComponent } from "../package-details/package-details.component";
import { CommonModule } from '@angular/common';
import { ViewSwitchService } from '../../services/view-switch.service';


@Component({
  selector: 'app-package-plan',
  standalone: true,
  imports: [SharedBtnComponent, PackageDetailsComponent, CommonModule],
  templateUrl: './package-plan.component.html',
  styleUrl: './package-plan.component.scss'
})
export class PackagePlanComponent {
  @Input() badge?: string | null = "الأكثر مبيعاً";
  @Input() planTitle?: string = 'خطة المبتدئين';
  @Input() price?: string = '3840';
  @Input() currency = 'جنيه';
  @Input() billingCycle = 'شهريًا';
  
   private viewSvc = inject(ViewSwitchService);
   goToSubscription() {
    this.viewSvc.go('subscription');
  }
}
