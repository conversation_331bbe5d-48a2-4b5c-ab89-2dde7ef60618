import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MainLayoutComponent } from 'src/app/modules/layouts/main-layout/main-layout.component';
import { homeResolver } from './home.resolver';
import { HomeComponent } from './pages/home.component';
import { BusinessPackageCycleComponent } from '../business-package/pages/business-package-cycle/business-package-cycle.component';

const routes: Routes = [
  {
    path: '',
    component: MainLayoutComponent,
    children: [
      {
        path: '',
        component: BusinessPackageCycleComponent,
        resolve: {
          homeResolver: homeResolver
        }
      }, {
        path: 'page/:page',
        component: HomeComponent
      },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class HomeRoutingModule { }
