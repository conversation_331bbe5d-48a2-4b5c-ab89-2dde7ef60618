import { Meta, StoryObj, moduleMetadata } from "@storybook/angular";
import { PackagePlanComponent } from "./package-plan.component";
import { PackageDetailsComponent } from "../package-details/package-details.component";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";

const meta: Meta<PackagePlanComponent> = {
  title: "BusinessPackages/BusinessPackagePlan",
  component: PackagePlanComponent,
  tags: ["autodocs"],
  decorators: [
    moduleMetadata({
      imports: [PackageDetailsComponent, SharedBtnComponent],
    }),
  ],
  render: (args) => ({
    props: args,
  }),
};

export default meta;
type Story = StoryObj<PackagePlanComponent>;

export const Default: Story = {
  args: {
    badge: "الأكثر مبيعاً",
    planTitle: "خطة المبتدئين (Starter Plan) ",
    price: '3,840',
    currency: "جنيه",
    billingCycle: "شهريًا",
  },
};

export const BusinessPro: Story = {
  args: {
    badge: null,
    planTitle: "برو للأعمال (Business Pro) ",
    price: '7,000',
    currency: "جنيه",
    billingCycle: "شهريًا",
  },
};
export const PowerSeller: Story = {
  args: {
    badge: null,
    planTitle: "باور سيلر (Power Seller)",
    price: '13,250',
    currency: "جنيه",
    billingCycle: "شهريًا",
  },
};