<!-- <a [routerLink]="['/test-event']">Go to send event</a> -->
<div *ngIf="listing$ | async as listing;else skeleton" class="body_wrap">

  <ng-container [ngSwitch]="viewMode">
    <ng-container *ngSwitchCase="true">
      @defer () {
      <ng-container *ngTemplateOutlet="content"></ng-container>

      }
    </ng-container>
    <ng-container *ngSwitchDefault>
      <ng-container *ngTemplateOutlet="content"></ng-container>
    </ng-container>
  </ng-container>
  <ng-template #content>
    <app-ad-top-links *ngIf="!viewMode" [listing]="listing" [images]="urls | listingImagesUrl"></app-ad-top-links>

    <div class="container">

      <a *ngIf="viewMode" class="back_btn" (click)="backToMyListings()">
        <i class="pi pi-chevron-left"></i>{{ "back" | translate }}
      </a>

      <div>
        <div class="row">
          <div class="col-lg-7">
            <app-item-carousel [imgName]="listing.name" *ngIf="urls.length > 0" [items]="urls | listingImagesUrl"
              [viewMode]="viewMode || (listing | isViewMode)"></app-item-carousel>

            <div>
              <div class="item_name">
                <h1 class="item_title">{{ listing?.name }} </h1>
                <div class="icon_text">
                  <span>{{ listing.areaName | translate }}<i class="dot"></i>{{ listing.firstApprovalDate ??
                    listing.createdDate | fromNow }}</span>


                  <div class="views_info d-lg-none" *ngIf="listing | isViewMode">
                    <b>{{ listing.views }}</b> {{ "Views" | translate }}
                  </div>
                </div>
              </div>
              <div class="d-lg-none" *ngIf="device.isMobile">
                <ng-container [ngTemplateOutlet]="priceBox"></ng-container>
              </div>
              <ul class="itemProps" [ngClass]="{'more': thereIsMore}">
                <li [hidden]="category | hidelisting">
                  <span>{{ "Condition" | translate }}</span>
                  <span>{{ listing?.conditionName | translate }}</span>
                </li>
                <ng-container *ngFor="let item of listing?.properties;trackBy : trackby">
                  <ng-container *ngIf="item.propTypeName != 'Boolean'">
                    <li *ngIf="item.value">
                      <span>{{ item.name | translate }}</span>
                      <span>{{ item.propTypeName == 'DropDown' || item.propTypeName == 'RadioButton' ? (item.value |
                        translate) : item.value }}</span>
                    </li>
                  </ng-container>
                </ng-container>
              </ul>
              <div class="more_row" *ngIf="thereIsMore">
                <div (click)="thereIsMore = false">{{ 'More' | translate }}</div>
              </div>
              <div class="description">
                <h2>{{ 'Description' | translate }}</h2>




                <div *isAuthorized="true"
                  [innerHTML]="this.viewMode ? listing?.description : (listing?.description | linkParser : 'Phonenumber' | async)"
                  (click)="descriptionClicked($event)" VerifiedClick></div>

                <div *isAuthorized="false"
                  [innerHTML]="this.viewMode ? listing?.description : (listing?.description | linkParser : 'loginPhonenumber' | async)"
                  (click)="descriptionClicked($event)" VerifiedClick></div>
              </div>
              <div class="itemTags" *ngIf="listing?.keyWords && listing?.keyWords.length > 0">
                <h3>{{ 'Tags' | translate }}</h3>
                <div class="tagsList">
                  <ng-container *ngFor="let item of listing?.keyWords;trackBy : trackbyName">
                    <a [routerLink]="'/tag/' + (item.name | slugTags : '-')">
                      {{ item.name | translate }}
                    </a>
                  </ng-container>
                </div>
              </div>
              <div class="itemFeatures" *ngIf="hasFeatures(listing?.properties)">
                <h2>{{ 'Features' | translate }}</h2>
                <ul>
                  <ng-container *ngFor="let item of listing?.properties;trackBy : trackby">
                    <ng-container *ngIf="item.propTypeName == 'Boolean' && item.value != ''">
                      <li>
                        {{ item.name | translate }}
                      </li>
                    </ng-container>
                  </ng-container>
                </ul>
              </div>


              <!-- Seller and Comments -->
              <div class="seller_info">
                @defer () {
                <app-ad-seller-info [listing]="listing" [isBussnisLogo]="device.isMobile"
                  [showSeeProfile]="false"></app-ad-seller-info>
                <app-ad-comments [listingID]=" listingID"></app-ad-comments>

                <div class="mt-3" *ngIf="!device.isDesktop">
                  <app-listing-instructions [viewMode]="listing | isViewMode"
                    [listingID]="listingID"></app-listing-instructions>
                </div>
                }
              </div>
              <div class="d-lg-none action-box bottom-sticky" *ngIf="device.isMobile || device.isTablet">
                <ng-container [ngTemplateOutlet]="actionBox"></ng-container>
              </div>
            </div>
          </div>
          <div class="col-lg-5 side-col">
            <div class="d-none d-lg-block" *ngIf="device.isDesktop">
              <ng-container [ngTemplateOutlet]="priceBox"></ng-container>
              <ng-container [ngTemplateOutlet]="swappOptions"></ng-container>
              <app-listing-instructions *ngIf="!(listing | isViewMode)" [viewMode]="listing | isViewMode"
                [listingID]="listingID"></app-listing-instructions>
              <app-listing-offers *ngIf="(listing | isViewMode) && listing.status != ListingStatus.created"
                [listingID]="listingID"></app-listing-offers>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ng-container *ngIf="!viewMode">
      @defer () {
      <div *ngIf="recommendedlisting$ | async as recommendedlisting">
        <div class="slider_holder">
          <app-items-slider [loop]="false" class="bottom_theme" *ngIf="recommendedlisting.length > 0"
            [itemsListing]="recommendedlisting" [title]="'You_May_Also_Like' | translate"></app-items-slider>
        </div>
      </div> }
    </ng-container>

    <ng-template #priceBox>
      <div *ngIf="listing?.price! > 0" class="item_price">
        <span [innerHTML]="listing?.price | ncurrency"></span>
      </div>

      <div
        [ngClass]="{ 'listing_specs' : !(listing?.treadBy && (listing?.treadBy.length > 0)) && !(listing?.interedtedCategoris && (listing?.interedtedCategoris.length > 0))}">
        <app-swappable-items [intersetedCategories]="listing?.interedtedCategoris ?? []" [tradeBy]="listing?.treadBy!"
          [isCash]="listing?.paymentMethod == ListingPaymentMethod.cashandswapp"
          *ngIf="listing?.paymentMethod == ListingPaymentMethod.swapp || listing?.paymentMethod == ListingPaymentMethod.cashandswapp">

        </app-swappable-items>

        <div class="cash_icon"
          *ngIf="listing.paymentMethod == ListingPaymentMethod.cash || (!(listing?.treadBy.length > 0)&& !(listing?.interedtedCategoris.length > 0) && listing?.paymentMethod == ListingPaymentMethod.cashandswapp )">
          <svg data-id="cash_icon" viewBox="0 0 15 12" fill="nones">
            <g opacity="0.5">
              <path
                d="M14.25 0H0.75C0.551088 0 0.360322 0.0790178 0.21967 0.21967C0.0790176 0.360322 0 0.551088 0 0.75V11.25C0 11.4489 0.0790176 11.6397 0.21967 11.7803C0.360322 11.921 0.551088 12 0.75 12H14.25C14.4489 12 14.6397 11.921 14.7803 11.7803C14.921 11.6397 15 11.4489 15 11.25V0.75C15 0.551088 14.921 0.360322 14.7803 0.21967C14.6397 0.0790178 14.4489 0 14.25 0ZM13.5 8.25C12.9033 8.25 12.331 8.48705 11.909 8.90901C11.4871 9.33097 11.25 9.90326 11.25 10.5H3.75C3.75 9.90326 3.51295 9.33097 3.09099 8.90901C2.66903 8.48705 2.09674 8.25 1.5 8.25V3.75C2.09674 3.75 2.66903 3.51295 3.09099 3.09099C3.51295 2.66903 3.75 2.09674 3.75 1.5H11.25C11.25 2.09674 11.4871 2.66903 11.909 3.09099C12.331 3.51295 12.9033 3.75 13.5 3.75V8.25Z"
                fill="black" />
              <path
                d="M7.5 3C5.8455 3 4.5 4.3455 4.5 6C4.5 7.6545 5.8455 9 7.5 9C9.1545 9 10.5 7.6545 10.5 6C10.5 4.3455 9.1545 3 7.5 3ZM7.5 7.5C6.67275 7.5 6 6.82725 6 6C6 5.17275 6.67275 4.5 7.5 4.5C8.32725 4.5 9 5.17275 9 6C9 6.82725 8.32725 7.5 7.5 7.5Z"
                fill="black" />
            </g>
          </svg>

          <span>{{ 'Cash' | translate}}</span>
        </div>



        <div class="adoption_icon" *ngIf="listing?.properties | forAdoption | async">
          <svg width="13" height="13" viewBox="0 0 13 13" fill="none">
            <g clip-path="url(#clip0_1466_26607)">
              <path
                d="M6.50001 5.68768C4.48374 5.68768 1.62501 8.80463 1.62501 10.7722C1.62501 11.6583 2.30573 12.1877 3.44654 12.1877C4.68661 12.1877 5.50546 11.5509 6.50001 11.5509C7.50319 11.5509 8.32433 12.1877 9.55349 12.1877C10.6943 12.1877 11.375 11.6583 11.375 10.7722C11.375 8.80463 8.51628 5.68768 6.50001 5.68768ZM2.76048 5.3675C2.49642 4.48772 1.6829 3.91795 0.943527 4.09467C0.204152 4.27139 -0.181023 5.12782 0.0830391 6.0076C0.347102 6.88739 1.16062 7.45715 1.89999 7.28043C2.63937 7.10371 3.02454 6.24729 2.76048 5.3675ZM4.91157 4.83989C5.69716 4.63321 6.09021 3.57188 5.78958 2.46942C5.48896 1.36696 4.60841 0.641038 3.82282 0.847718C3.03724 1.0544 2.64419 2.11573 2.94482 3.21819C3.24544 4.32065 4.12624 5.04682 4.91157 4.83989ZM12.0562 4.09492C11.3169 3.91821 10.5036 4.48797 10.2393 5.36776C9.97523 6.24754 10.3604 7.10397 11.0998 7.28069C11.8392 7.4574 12.6524 6.88764 12.9167 6.00785C13.1808 5.12807 12.7956 4.27164 12.0562 4.09492ZM8.08845 4.83989C8.87403 5.04657 9.75458 4.32065 10.0552 3.21819C10.3558 2.11573 9.96278 1.05465 9.1772 0.847718C8.39161 0.640784 7.51107 1.36696 7.21044 2.46942C6.90982 3.57188 7.30286 4.63321 8.08845 4.83989Z"
                fill="#00A51E" />
            </g>
            <defs>
              <clipPath id="clip0_1466_26607">
                <rect width="13" height="13" fill="white" />
              </clipPath>
            </defs>
          </svg>

          {{ 'for_adoption' | translate}}

        </div>
      </div>




      <div *ngIf="!(listing | isViewMode) && listing.status == ListingStatus.DealInProgress">
        <span class="lising_status_icon">
          <svg data-id="inner_time_icon" viewBox="0 0 15 18" fill="none">
            <path
              d="M7.48481 16.6044C11.0663 16.6044 13.9696 13.701 13.9696 10.1196C13.9696 6.53812 11.0663 3.63477 7.48481 3.63477C3.90335 3.63477 1 6.53812 1 10.1196C1 13.701 3.90335 16.6044 7.48481 16.6044Z"
              fill="#FAAF40" fill-opacity="0.18" stroke="#FAAF40" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
            <path d="M7.48438 10.1192L9.9825 7.62109" stroke="#FAAF40" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
            <path d="M5.71875 1.06055H9.25592" stroke="#FAAF40" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>

          <span>{{ 'Hurry Deal in progress' | translate}}</span>
        </span>
      </div>
    </ng-template>

    <ng-template #swappOptions>
      <ng-container *ngIf="!viewMode && !(listing | isViewMode)">
        <ng-container *ngIf="listing?.lastUserOffer">
          <app-ad-checkoffer-btn [offer]="listing?.lastUserOffer!"></app-ad-checkoffer-btn>
        </ng-container>
        <ng-container *ngIf="!listing?.lastUserOffer">
          <app-ad-swapp-options [listing]="listing" [isDesktop]="!device.isMobile"
            (onShowMobile)="callCustomer()"></app-ad-swapp-options>
        </ng-container>
      </ng-container>


      <app-ad-status-view *ngIf="listing | isViewMode" [listing]="listing"></app-ad-status-view>

    </ng-template>

    <ng-template #actionBox>
      <ng-container *ngIf="!viewMode && !(listing | isViewMode)">
        <div *ngIf="listing?.lastUserOffer" class="mt-3 mb-3">
          <app-ad-checkoffer-btn [offer]="listing?.lastUserOffer"></app-ad-checkoffer-btn>
        </div>

        <ng-container>
          <div *ngIf="!listing?.lastUserOffer" class="item_actions">
            <app-gray-btn VerifiedClick [withSvg]="true" icon="#comment_icon" [btnText]="'Message' | translate"
              btnType="button" [midBtn]="true" (VerifiedClick)="scrollToComments()"></app-gray-btn>
            <app-gray-btn *ngIf="canViewPhone()" [skipAuth]="forcePhoneVisible" VerifiedClick [withSvg]="true"
              icon="#phone_icon" [btnText]="'Call' | translate" btnType="button" [midBtn]="true"
              (VerifiedClick)="callCustomer()" (unVerifiedClick)="sendEvent($event)"></app-gray-btn>
            <app-secondary-btn VerifiedClick [btnText]="'Make an Offer' | translate " btnType="button" [midBtn]="true"
              (VerifiedClick)="offerMenuVisible = true"></app-secondary-btn>
          </div>
        </ng-container>
      </ng-container>


      <app-ad-status-view *ngIf="listing | isViewMode" [listing]="listing"></app-ad-status-view>
    </ng-template>
    <p-dialog header="" [closable]="true" [className]="'offer_form'" [modal]="true" [(visible)]="offerMenuVisible"
      [style]="{width: '50vw'}" (onHide)="resetValues()">
      <div class="modelTop">
        <ng-container [ngTemplateOutlet]="swappOptions"></ng-container>
      </div>
      <div class="model_actions"></div>
    </p-dialog>
  </ng-template>

</div>





<ng-template #skeleton>
  <app-item-details-skeleton></app-item-details-skeleton>
</ng-template>