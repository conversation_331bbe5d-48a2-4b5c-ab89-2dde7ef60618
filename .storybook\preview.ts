import { HttpClient } from "@angular/common/http";
import { TranslateService } from "@ngx-translate/core";
import { setCompodocJson } from "@storybook/addon-docs/angular";
import { componentWrapperDecorator, moduleMetadata, type Preview } from '@storybook/angular';
import docJson from "../documentation.json";
import { SBTranslateModule } from './i18-translate.loader';
import { SBLocaleManagerComponent } from './i18n-management.component';

setCompodocJson(docJson);


const preview: Preview = {
  decorators: [
    moduleMetadata({
      declarations: [SBLocaleManagerComponent],
      imports: [SBTranslateModule],
      providers: [HttpClient, TranslateService],
    }),
    (story, context) => {
      const { locale } = context.globals;

      // 1. Set HTML `dir` attribute based on locale
      const rtlLocales = ['ar-eg'];
      const isRTL = rtlLocales.includes(locale?.toLowerCase());

      // Wait for DOM to render and apply dir only to the story wrapper
      setTimeout(() => {
        const canvas = document.querySelector('#story--shared-listingcardrow--default--primary-inner') as HTMLElement;
        const canvas2 = document.getElementById('storybook-root') as HTMLElement;

        if (canvas) {
          canvas.setAttribute('dir', isRTL ? 'rtl' : 'ltr');
        }

        if (canvas2) {
          canvas2.setAttribute('dir', isRTL ? 'rtl' : 'ltr');
        }
      }, 0);

      const translateService = context.canvasElement?.ownerDocument?.defaultView?.['translateService'];
      if (translateService && locale) {
        const lang = locale;
        translateService.reloadLang(lang).subscribe(() => {
          translateService.use(lang);
        });
      }

      return componentWrapperDecorator(SBLocaleManagerComponent, { locale })(story, context);
    },
  ],
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
};

export const globalTypes = {
  locale: {
    name: 'Locale',
    title: 'Locale',
    description: 'Internationalization locale',
    toolbar: {
      icon: 'globe',
      items: [
        { value: 'ar-eg', title: 'Arabic' },
        { value: 'en-us', title: 'English' },
      ],
    },
  },
};

export default preview;