.profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;

  .profile-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 10px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .profile-info {
    text-align: center;

    .name {
      font-size: 16px;
      font-weight: 800;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .profile-type {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 12px;
      margin-top: 10px;

      .premium {
        color: var( --secondary-color);
        font-weight: 700;
        font-size: 12px;
      }

      .manage-btn {
        font-size: 10px;
        font-weight: 700;   
        border: 1px solid #72228233;
        background: transparent;
        color: var( --secondary-color);
        padding: 4px 12px;
        border-radius: 6px;
        cursor: pointer;
      }
    }
  }
}
.rating-container {
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  gap: 4px;
}

.rating-text {
  text-align: center;
  color: var(--Gray, #A3A4A5);
  font-size: 10px;
  font-family: Cairo, sans-serif;
  font-weight: 700;
  line-height: 28px;
  word-wrap: break-word;
  text-box-trim: trim-both;
  text-box-edge: cap alphabetic;
}

.rating-stars {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 4px;
}

.star-icon {
  position: relative;
}

.star-icon svg {
  width: 14px;
  height: 14px;
}
