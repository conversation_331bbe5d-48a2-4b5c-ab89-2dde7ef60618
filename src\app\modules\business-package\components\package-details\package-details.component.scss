.plan-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.price-section {
  display: flex;
  direction: rtl;
  flex-direction: column;
}
.price {
  color: #722282; 
  font-weight: 800;
  font-size: 24px;
  line-height: 28px;
}

.price-unit {
  font-size: 12px;
  font-weight: 600;
  margin-right: 4px;
}

.plan-title {
  color: #722282;
  font-size: 14px;
  font-weight: 700;
}
.package-details {
  text-align: right;
  direction: rtl;
  color: #272728;
  font-size: 14px;
}

.item {
  margin-bottom: 8px;
}

.label {
  font-weight: 700;
}

.value {
  font-weight: 400;
  display: inline;
}
li {
    list-style: none;
}
.dot-icon {
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: #272728;
  margin-left: 6px;
}
.renewal-note {
  width: 287px;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  gap: 6px;
  margin-top: 12px;

  .note-text {
    font-size: 10px;
    font-weight: 700;
    color: #7C7C7C;
    line-height: 28px;
    text-align: right;
  }

  .note-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
}

