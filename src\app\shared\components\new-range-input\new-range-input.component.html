<div class="ads-range rtl" dir="rtl">
  <div class="ads-range__header">{{ header }}</div>

  <div class="ads-range__row">
    
    <div class="ads-range__min">{{min}}</div>

    <div class="ads-range__track">
      <input 
      id="adsRange"
      type="range"
      [attr.min]="min"
      [attr.max]="max"
      [attr.value]="value"
      [attr.step]="step"
      (input)="onRangeChange($event)"
      [ngStyle]="{background:'linear-gradient(to left, #FAAF40 ' + progressPercent + '%, #D9D9D9 0)' }" />
    </div>

    <div class="ads-range__value">
      <output id="adsOut">{{value}}</output>/<span id="adsMax">{{max}}</span>
    </div>
  </div>
</div>
