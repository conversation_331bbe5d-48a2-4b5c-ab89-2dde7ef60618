import { Component } from "@angular/core";
import { FormBuilder, FormsModule, ReactiveFormsModule } from "@angular/forms";
import { SvgIconsComponent } from '../../../../shared/components/svg-icons/svg-icons.component';
import { NewProfileHeaderComponent } from "../../components/new-profile-header/new-profile-header.component";
@Component({
  selector: "app-new-my-profile",
  standalone: true,
  imports: [ FormsModule, ReactiveFormsModule, SvgIconsComponent,NewProfileHeaderComponent],
  templateUrl: "./new-my-profile.component.html",
  styleUrl: "./new-my-profile.component.scss",
})
export class NewMyProfileComponent {
  percentage: number = 70;
  isSelected = false;

  form = this.fb.group({
    selections: [[]],
  });

  constructor(private fb: FormBuilder) {}

  toggleSelection() {
    this.isSelected = !this.isSelected;
  }
}