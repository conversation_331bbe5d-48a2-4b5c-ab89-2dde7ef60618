.ratings-wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 0 24px;

  .ratings-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;

    .ratings-title {
      font-size: 16px;
      font-weight: 800;
      color: #272728;
    }
  }

  .ratings-summary {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .summary-info {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 16px;

      .stars {
        color: #faaf40;
        font-size: 14px;

        .rating-count {
          display: flex;
          justify-content: flex-end;
          gap: 4px;
          font-size: 10px;
          color: #a3a4a5;
          font-weight: 700;
        }
      }

      .total-rating {
        font-size: 26px;
        font-weight: 800;
        color: #272728;
      }
    }

    .distribution {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .row {
        display: flex;
        align-items: center;
        gap: 8px;

        .count {
          width: 30px;
          font-size: 12px;
          color: #7c7c7c;
          text-align: center;
        }

        .bar {
          flex: 1;
          background-color: #d9d9d9;
          height: 6px;
          border-radius: 100px;
          overflow: hidden;
          direction: rtl;
          padding-right: 0px;

          .fill {
            background-color: #faaf40;
            height: 100%;
            border-radius: 100px;
          }
        }
        .star-display {
          width: 31px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          gap: 4px;

          .star-icon {
            position: relative;
          }

          .star-number {
            color: var(--Gray-2, #7c7c7c);
            font-size: 12px;
            font-weight: 700;
            line-height: 15.5px;
            word-wrap: break-word;
          }
        }
      }
    }
  }

  .review {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .stars {
      color: #faaf40;
      font-size: 14px;
      direction: rtl;
    }

    .meta {
      display: flex;
      justify-content: space-between;
      font-size: 10px;
      color: #7c7c7c;

      .user {
        font-size: 14px;
        font-weight: 700;
        color: #272728;
      }
    }

    .ad {
      display: flex;
      gap: 4px;
      font-size: 12px;
      color: #7c7c7c;
      direction: rtl;

      .label {
        font-weight: 700;
      }
    }

    .comment {
      direction: rtl;
      .label {
        font-weight: 700;
        font-size: 12px;
        color: #7c7c7c;
      }

      .text {
        font-weight: 600;
        font-size: 12px;
        color: #7c7c7c;
      }
    }

    hr {
      border: none;
      border-top: 1px solid rgba(114, 34, 130, 0.1);
      margin-top: 8px;
    }
  }
}
