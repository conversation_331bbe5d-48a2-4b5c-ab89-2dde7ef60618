<div class="profile-header">
  <div class="profile-image">
    <img [src]="imageUrl" alt="Profile Image" />
  </div>

  <div class="profile-info">
    <h2 class="name">
      <app-svg-icons
        name="icon-check"
        width="21px"
        height="21px"
      ></app-svg-icons>
      {{ name }}
    </h2>

    <div class="rating-container">
      <div class="rating-text"> التقييمات </div>
      <div class="rating-text">23 | </div>
      <div class="rating-text">3.0</div>
      <div class="rating-stars">
        <div class="star-icon" data-svg-wrapper>
          <svg
            viewBox="0 0 14 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7 1.16666L8.8025 4.81832L12.8333 5.40749L9.91667 8.24832L10.605 12.2617L7 10.3658L3.395 12.2617L4.08333 8.24832L1.16667 5.40749L5.1975 4.81832L7 1.16666Z"
              stroke="var(--Secondary, #FAAF40)"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
        <div class="star-icon" data-svg-wrapper>
          <svg
            viewBox="0 0 14 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7 1.16666L8.8025 4.81832L12.8333 5.40749L9.91667 8.24832L10.605 12.2617L7 10.3658L3.395 12.2617L4.08333 8.24832L1.16667 5.40749L5.1975 4.81832L7 1.16666Z"
              stroke="var(--Secondary, #FAAF40)"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
        <div class="star-icon" data-svg-wrapper>
          <svg
            viewBox="0 0 14 14"
            fill="var(--Secondary, #FAAF40)"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7 1.16666L8.8025 4.81832L12.8333 5.40749L9.91667 8.24832L10.605 12.2617L7 10.3658L3.395 12.2617L4.08333 8.24832L1.16667 5.40749L5.1975 4.81832L7 1.16666Z"
              fill="var(--Secondary, #FAAF40)"
              stroke="var(--Secondary, #FAAF40)"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
        <div class="star-icon" data-svg-wrapper>
          <svg
            viewBox="0 0 14 14"
            fill="var(--Secondary, #FAAF40)"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7 1.16666L8.8025 4.81832L12.8333 5.40749L9.91667 8.24832L10.605 12.2617L7 10.3658L3.395 12.2617L4.08333 8.24832L1.16667 5.40749L5.1975 4.81832L7 1.16666Z"
              fill="var(--Secondary, #FAAF40)"
              stroke="var(--Secondary, #FAAF40)"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
        <div class="star-icon" data-svg-wrapper>
          <svg
            viewBox="0 0 14 14"
            fill="var(--Secondary, #FAAF40)"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7 1.16666L8.8025 4.81832L12.8333 5.40749L9.91667 8.24832L10.605 12.2617L7 10.3658L3.395 12.2617L4.08333 8.24832L1.16667 5.40749L5.1975 4.81832L7 1.16666Z"
              fill="var(--Secondary, #FAAF40)"
              stroke="var(--Secondary, #FAAF40)"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>
    </div>
    <div class="profile-type">
      <button class="manage-btn">إدارة الحساب</button>
      <span class="premium">حساب باقة مميز</span>
    </div>
  </div>
</div>
