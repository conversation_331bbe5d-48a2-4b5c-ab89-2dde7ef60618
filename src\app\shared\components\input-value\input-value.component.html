<div class="points-card rtl" dir="rtl">
  <div class="points-card__header">{{ header }}</div>
  <label class="points-input">
    <input
      [attr.type]="showPassword ? 'text' : type"
      class="points-input__field"
      [attr.value]="value"
      min="0"
      (input)="onValueChange($event)"
    />
  <ng-container *ngIf="type === 'password'">
      <svg
        *ngIf="showPassword"
        (click)="togglePasswordVisibility()"
        width="17"
        height="16"
        viewBox="0 0 17 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        style="cursor: pointer;"
      >
        <path
          d="M8.64062 4C5.70729 4 3.21729 5.66667 1.64062 8C3.21729 10.3333 5.70729 12 8.64062 12C11.5739 12 14.0639 10.3333 15.6406 8C14.0639 5.66667 11.5739 4 8.64062 4ZM8.64062 10.3333C7.35396 10.3333 6.30729 9.28667 6.30729 8C6.30729 6.71333 7.35396 5.66667 8.64062 5.66667C9.92729 5.66667 10.9739 6.71333 10.9739 8C10.9739 9.28667 9.92729 10.3333 8.64062 10.3333Z"
          stroke="#ACB5BB"
          stroke-width="1.3"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      <svg
        *ngIf="!showPassword"
        (click)="togglePasswordVisibility()"
        width="17"
        height="16"
        viewBox="0 0 17 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        style="cursor: pointer;"
      >
        <path
          d="M7.69733 7.05794C7.44732 7.30804 7.3069 7.64721 7.30697 8.00085C7.30703 8.35448 7.44757 8.6936 7.69767 8.94361C7.94777 9.19362 8.28694 9.33404 8.64057 9.33398C8.9942 9.33392 9.33332 9.19338 9.58333 8.94328M11.7613 11.1154C10.8261 11.7005 9.74373 12.0073 8.64063 12C6.24063 12 4.24063 10.6667 2.64062 8.00002C3.48862 6.58669 4.44863 5.54802 5.52063 4.88402M7.42729 4.12002C7.82665 4.03917 8.23317 3.99897 8.64063 4.00002C11.0406 4.00002 13.0406 5.33335 14.6406 8.00002C14.1966 8.74002 13.7213 9.37802 13.2153 9.91335M2.64062 2L14.6406 14"
          stroke="#ACB5BB"
          stroke-width="1.3"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </ng-container>
  </label>
</div>
