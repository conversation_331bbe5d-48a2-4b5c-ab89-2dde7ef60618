import { Component, Input } from '@angular/core';
import { SvgIconsComponent } from '@src/app/shared/components/svg-icons/svg-icons.component';
import { NtranslatePipe } from '@src/app/shared/pipes/ntranslate.pipe';

@Component({
  selector: 'app-new-profile-header',
  standalone: true,
  imports: [SvgIconsComponent, NtranslatePipe],
  templateUrl: './new-profile-header.component.html',
  styleUrl: './new-profile-header.component.scss'
})
export class NewProfileHeaderComponent {
  @Input() name: string = 'حسام نصر';
  @Input() imageUrl: string = 'assets/images/profile-avatar.jpg';
}