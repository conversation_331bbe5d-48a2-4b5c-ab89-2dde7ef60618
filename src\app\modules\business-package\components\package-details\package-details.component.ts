import { CommonModule } from "@angular/common";
import { Component, Input, ViewEncapsulation } from "@angular/core";

@Component({
  selector: "app-package-details",
  standalone: true,
  imports: [CommonModule],
  templateUrl: "./package-details.component.html",
  styleUrl: "./package-details.component.scss",
  encapsulation: ViewEncapsulation.None,
})
export class PackageDetailsComponent {
  @Input() duration!: string;
  @Input() adCount!: number;
  @Input() availablePoints!: number;
  @Input() features: string[] = [];
  @Input() support!: string;
  @Input() renewalNote: string = "";
  @Input() planTitle?: string = 'خطة المبتدئين';
  @Input() price?: string = '3840';
  @Input() currency = 'جنيه';
  @Input() billingCycle = 'شهريًا';
}
