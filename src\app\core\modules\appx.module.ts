import { NgModule } from "@angular/core";
import { BrowserService } from "@src/app/modules/core/service/browser.service";
import { LocalService } from "@src/app/modules/core/service/local.service";
import { ApmxService } from '../services/apmx.service';

@NgModule({
    imports: [

    ],
    providers: [
        ApmxService,

        LocalService,


    ]
})
export class AppxModule {
    constructor(
        service: ApmxService,
        browser: BrowserService,
        localService: LocalService

    ) {

        if (browser.isBrowser && !browser.isSlowNetwork()) {
            service.init();

        }


    }

    initApm() {

    }
}
