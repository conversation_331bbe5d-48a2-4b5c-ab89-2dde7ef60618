<app-categories-tabs class="d-lg-block"></app-categories-tabs>

<div class="home-top-widgets">
  <app-hero-section></app-hero-section>


</div>
<div>
  <div *isAuthorized="true">
    <ng-container *ngIf="pickslisting$ | async as pickslisting">
      <div>
        <app-items-slider [itemsListing]="pickslisting" [title]="'Picks for you' | translate"></app-items-slider>
      </div>
    </ng-container>
  </div>
  <div *ngIf="homeSections" class="home_sections_box">

    <div *ngFor="let item of homeSections ; let i = index;trackBy : trackby">

      @if(i < (homeSections | middleSection)) { <app-items-slider *ngIf="item.listings && item.listings.length > 0"
        [h2]="true" [link]="item" [itemsListing]="item.listings | splitList : deviceDetection.isMobile ? 4 : 0"
        [title]="item.label | translate"></app-items-slider>
        }@else if(i == (homeSections | middleSection)){
        <app-smart-banner *isAuthorized="false"></app-smart-banner>
        <app-items-slider *ngIf="item.listings && item.listings.length > 0" [h2]="true" [link]="item"
          [itemsListing]="item.listings | splitList : deviceDetection.isMobile ? 4 : 0"
          [title]="item.label | translate"></app-items-slider>
        }@else {

        <div #viewport>
          @defer (on viewport(viewport)) {
          <app-items-slider *ngIf="item.listings && item.listings.length > 0" [h2]="true" [link]="item"
            [itemsListing]="item.listings | splitList : deviceDetection.isMobile ? 4 : 0"
            [title]="item.label | translate"></app-items-slider>} @placeholder {
          <div></div>
          }
        </div>
        }

    </div>
  </div>

</div>



<ng-template #loader>
  <app-listing-slider-skeleton></app-listing-slider-skeleton>
</ng-template>