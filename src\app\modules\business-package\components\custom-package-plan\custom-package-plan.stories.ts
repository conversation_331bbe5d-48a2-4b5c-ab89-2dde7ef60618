import { Meta, StoryObj } from '@storybook/angular';
import { CustomPackagePlanComponent } from './custom-package-plan.component';

const meta: Meta<CustomPackagePlanComponent> = {
  title: 'BusinessPackages/CustomPackagePlan',
  component: CustomPackagePlanComponent,
  tags: ['autodocs'],
  render: (args: CustomPackagePlanComponent) => ({
    props: args,
  }),
};

export default meta;
type Story = StoryObj<CustomPackagePlanComponent>;

export const Default: Story = {
  args: {
    duration: '٩٠ يومًا ',
    adCount: 'حسب الطلب',
    availablePoints: 'حسب الطلب',
    features: [
      ' جميع ميزات Power Seller',
      'تكامل API',
      'تحليلات تفصيلية من مركز التاجر',
      ' تصدير بيانات التفاعل المرتبطة بعملاء محددين',
    ],
    support: 'اتفاقية مستوى خدمة خاصة (SLA)',
    price: 'حسب العرض (Quotation)',
    renewalNote: 'سيتم تجديد تلقائياً بعد 90 يوم',

  },
};

