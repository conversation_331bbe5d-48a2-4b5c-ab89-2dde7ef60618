import { NgIf } from "@angular/common";
import { Component, Input, forwardRef } from "@angular/core";
import {
  ControlContainer,
  FormGroup,
  FormsModule,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from "@angular/forms";
import { SharedModule } from "../../shared.module";
import { SvgIconsComponent } from "../svg-icons/svg-icons.component";

@Component({
  selector: "app-check-box-form-controler",
  standalone: true,
  imports: [ReactiveFormsModule, FormsModule, NgIf, SharedModule, SvgIconsComponent],
  templateUrl: "./check-box-form-controler.component.html",
  styleUrl: "./check-box-form-controler.component.scss",
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CheckBoxFormControlerComponent),
      multi: true,
    },
  ],
})
export class CheckBoxFormControlerComponent {
  @Input({ required: true }) label: string = "";
  @Input({ required: true }) value: string = "";
  @Input() type: "checkbox" | "radio" = "checkbox";
  @Input() isDisabled = false;
  @Input() controlName: string = "selections";
  @Input() groupName: string = "radio-group";
  @Input() svgIconName: string = "";
  @Input() svgIconWidth: string = "";
  @Input() svgIconHeight: string = "";
  @Input() form?: FormGroup;

  constructor(private controlContainer: ControlContainer) {}

  get control() {
    return this.controlContainer.control?.get(this.controlName);
  }

  get isChecked(): boolean {
    if (!this.control?.value) return false;
    return this.type === "checkbox"
      ? this.control.value.includes(this.value)
      : this.control.value[0] === this.value;
  }

  get formGroup()  {
    return this.controlContainer.control as any;
  }

  toggleCheck() {
    if (!this.control || this.isDisabled) return;

    let updatedValues: string[];

    if (this.type === "checkbox") {
      const currentValues: string[] = this.control.value || [];
      updatedValues = this.isChecked
        ? currentValues.filter((v) => v !== this.value)
        : [...currentValues, this.value];
    } else {
      updatedValues = [this.value];
    }

    this.control.setValue(updatedValues);
    this.control.markAsDirty();
    this.control.markAsTouched();
  }
}
