<div class="terms-toggle" (click)="toggle()">
  <div class="label">{{ label }}</div>
  <div class="switch" [class.on]="checked">
    <svg
      width="44"
      height="24"
      viewBox="0 0 44 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0)">
        <rect
          width="44"
          height="24"
          rx="12"
          [attr.fill]="checked ? '#722282' : '#ccc'"
        />
        <circle [attr.cx]="checked ? 32 : 12" cy="12" r="10" fill="white" />
      </g>
      <defs>
        <clipPath id="clip0">
          <rect width="44" height="24" rx="12" fill="white" />
        </clipPath>
      </defs>
    </svg>
  </div>
</div>
