@import 'variables';
@import 'mixins';

.ad-card {
    background-color: $grey-2;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    padding: 10px;
    direction: inherit;
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 1rem;
}

.ad-card-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.ad-card-right {
    padding-left: 10px;
    display: flex;
}

.ad-card-left {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: flex-start;
}

.ad-card-right img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 8px;
    margin-right: 0.5rem;
}

.ad-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    margin-bottom: 10px;
    color: $text-color;
    width: 100%;
}

.metrics {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
    justify-content: space-between;
}

.metric {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 13px;
}

.metric .value {
    font-weight: bold;
    color: $text-color;
    margin-right: 0.1rem;
}

.metric .label {
    color: $text-muted-1;
}

.metric .icon {
    font-size: 14px;
    color: $warning;
}

.tags {
    display: flex;
    gap: 6px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.action-button {
    width: 100%;
}

.action-button button {
    background-color: white;
    border: 0.5px solid rgba($primary, 0.2);
    color: $primary;
    border-radius: 6px;
    padding: 5px;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    width: 100%;
    text-align: center;
}

.action-button .plus {
    font-weight: bold;
}

.ad-card.selected {
    background-color: rgba($primary, 0.1);
}

app-svg-icons {
    transform: rotate(var(--arrow-rotation));
}

.custom-checkbox {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
}

.custom-checkbox input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.checkmark {
    position: absolute;
    top: 0;
    right: 0;
    height: 20px;
    width: 20px;
    background-color: $grey-2;
    border-radius: 50%;
    border: 1px solid $line;
    transition: all 0.2s ease;
}

.custom-checkbox input:checked+.checkmark {
    background-color: $primary;
    border-color: $primary;
}

.custom-checkbox input:checked+.checkmark::after {
    content: "✓";
    position: absolute;
    right: 4px;
    top: 0;
    color: white;
    font-size: 14px;
}

.tag-label {
    background-color: rgba(124, 124, 124, 0.1);
    color: rgba(124, 124, 124, 1);
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    margin-right: 4px;
    display: inline-block;
}