import { Meta, StoryObj } from "@storybook/angular";
import { NewProfileHeaderComponent } from "./new-profile-header.component";

const meta: Meta<NewProfileHeaderComponent> = {
  title: "Profile/ProfileHeader",
  component: NewProfileHeaderComponent,
  tags: ["autodocs"],
  render: (args: NewProfileHeaderComponent) => ({
    component: NewProfileHeaderComponent,
    props: args,
  }),
};

export default meta;

type Story = StoryObj<NewProfileHeaderComponent>;

export const Default: Story = {
  args: {
    name: "حسام نصر",
    imageUrl: "assets/images/profile-avatar.jpg",
  },
};

export const WithCustomName: Story = {
  args: {
    name: "عبدالله ياسر",
    imageUrl: "assets/images/swapp_location.png",
  },
};
