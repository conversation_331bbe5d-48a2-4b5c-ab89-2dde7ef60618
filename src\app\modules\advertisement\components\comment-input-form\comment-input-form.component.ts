import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Store } from '@ngrx/store';
import { User } from '@src/app/authentication/models/dto/user.dto.model';
import { AlertHandlerService } from '@src/app/modules/core/alerts/alert-handler.service';
import { IsAuthorizedDirective } from '@src/app/shared/directives/isauthorized.directive';
import { LinkParserPipe } from '@src/app/shared/pipes/linkparser.pipe';
import { CommonService, ConversionEvents } from '@src/app/shared/services/common.service';
import { getUser } from '@src/app/store/app/selectors/app.selector';
import { LoaderIconComponent } from 'src/app/shared/components/loader-icon/loader-icon.component';
import { VerifiedClickDirective } from 'src/app/shared/directives/isverifiedclick.directive';

@Component({
  selector: 'app-comment-input-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, LoaderIconComponent, VerifiedClickDirective, LinkParserPipe, VerifiedClickDirective, IsAuthorizedDirective],
  templateUrl: './comment-input-form.component.html',
  styleUrls: ['./comment-input-form.component.scss']
})
export class CommentInputFormComponent implements OnInit {

  @Output() onSubmit = new EventEmitter();
  @Input() loading: boolean = false;
  @Input() placeholder: string = "";
  @Input() text: string = "";
  @Input() editMode: boolean = false;
  @Input() ListingID: number;



  title: string = "";
  currentUser: User;

  constructor(private store: Store,
    private alertModel: AlertHandlerService,
    private commonService: CommonService) { }

  ngOnInit(): void {
    this.title = this.text;

    this.store.select(getUser).subscribe(res => {
      if (!res) return;
      this.currentUser = res;
    });
  }

  submit() {
    this.onSubmit.emit(this.title);
  }

  clickOnComment(e) {
    if (e.target.classList.contains('phone-placeholder') && this.currentUser) {
      const phone = e.target.getAttribute('data-phone');
      if (phone) {
        this.callCustomer(phone);
      }
    }
  }

  callCustomer(phoneNumber?: string) {
    this.commonService.pushDataLayer({
      event: ConversionEvents.Contact_View,
      listing_id: this.ListingID,
      title: 'Show Phone',
      event_location: 'Comment',
    });
    this.alertModel.call({ phone: phoneNumber }, (e) => {
      if (e) {
        const telUri = `tel:${phoneNumber}`;
        window.location.href = telUri;
        this.commonService.pushDataLayer({
          event: ConversionEvents.Click_To_Call,
          listing_id: this.ListingID,
          title: 'Show Phone',
          event_location: 'Comment',
        });
        this.commonService.sendEvent('call_button_clicked', 'Call', this.ListingID);
      }
    });
  }
}
