import { Meta, StoryObj, moduleMetadata } from "@storybook/angular";
import { CancelConfirmationComponent } from "./cancel-confirmation.component";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";

const meta: Meta<CancelConfirmationComponent> = {
  title: "BusinessPackages/Shared/CancelConfirmation",
  component: CancelConfirmationComponent,
  tags: ["autodocs"],
  decorators: [
    moduleMetadata({
      imports: [SharedBtnComponent],
    }),
  ],
  render: (args: CancelConfirmationComponent) => ({
    props: args,
  }),
};

export default meta;
type Story = StoryObj<CancelConfirmationComponent>;

export const Default: Story = {
  args: {
    titleText: "هل أنت متأكد من رغبتك في إلغاء الباقة؟",
    cancelDate: "ستتم إلغاء الباقة في",
    cancelDateValue: "28 يناير 2024",
    btnText: "إلغاء تجديد الباقة",
  },
};

export const ChangeTitle: Story = {
  args: {
    titleText: "هل أنت متأكد من رغبتك في تحويل الي حساب فرد؟",
    cancelDate: "عند تحويل نوع الحساب ، سيتم إزالة أي إضافة او اعلانات تم شراؤها مسبقًا",
    cancelDateValue: null,
    btnText: "تحويل الي حساب فرد",
  },
};
