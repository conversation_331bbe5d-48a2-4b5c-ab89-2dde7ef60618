import { Meta, StoryObj } from '@storybook/angular';
import { TermsToggleComponent } from './terms-toggle.component';

const meta: Meta<TermsToggleComponent> = {
  title: 'Registration/Shared/TermsToggle',
  component: TermsToggleComponent,
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<TermsToggleComponent>;

export const Default: Story = {
  args: {
    checked: true,
    label: 'اوافق على الشروط والاحكام',
  },
};
export const Disabled: Story = {
  args: {
    checked: false,
    disabled: true,
    label: 'اوافق على الشروط والاحكام',
  },
};

export const Unchecked: Story = {
  args: {
    checked: false,
    label: 'اوافق على الشروط والاحكام',
  },
};

export const CustomLabel: Story = {
  args: {
    checked: true,
    label: 'عرض في صفحة الشركة',
  },
};
